#ifndef __FLASH_APP_H
#define __FLASH_APP_H

#include "HeaderFiles.h"

#define CONFIG_STORAGE_ADDR    0x001000
#define CONFIG_MAGIC_NUMBER    0x43464749

#define LOG_CACHE_ADDR         0x003000
#define LOG_CACHE_MAGIC        0x4C4F4743
#define LOG_CACHE_MAX_SIZE     512

typedef struct {
    uint32_t magic;           
    float ratio_value;        
    float limit_value;        
    uint32_t sample_interval; 
    uint32_t checksum;        
} config_storage_t;



extern uint32_t flash_id; 
extern uint32_t current_log_id;

void Flash_Init(void);
uint8_t Save_Config_To_Flash(float ratio, float limit, uint32_t interval);
uint8_t Read_Config_From_Flash(float* ratio, float* limit, uint32_t* interval);
uint8_t Verify_Config_Data(config_storage_t* data);
uint32_t Calculate_Config_Checksum(config_storage_t* data);

uint8_t Log_Init(void);

uint8_t Cache_Log_To_Flash(const char* log_msg);
uint8_t Check_Cached_Log_In_Flash(void);
uint8_t Write_Cached_Log_To_Log0(void);

/******************** 三通道 *******************/
// 三通道配置存储地址和魔数
#define MULTI_CONFIG_STORAGE_ADDR  0x002000
#define MULTI_CONFIG_MAGIC_NUMBER  0x4D434647  // "MCFG"
// 三通道配置存储结构体 (不包含采样间隔，采样间隔使用原有方式存储)
typedef struct {
    uint32_t magic;           // 魔数标识
    float ch0_ratio;          // 通道0变比值
    float ch1_ratio;          // 通道1变比值
    float ch2_ratio;          // 通道2变比值
    float ch0_limit;          // 通道0阈值
    float ch1_limit;          // 通道1阈值
    float ch2_limit;          // 通道2阈值
    uint32_t checksum;        // 校验和
} multi_channel_config_t;


// 三通道配置Flash存储函数声明 (采样间隔使用原有方式存储)
uint8_t Save_Multi_Channel_Config_To_Flash(float ch0_ratio, float ch1_ratio, float ch2_ratio,
                                          float ch0_limit, float ch1_limit, float ch2_limit);
uint8_t Read_Multi_Channel_Config_From_Flash(float* ch0_ratio, float* ch1_ratio, float* ch2_ratio,
                                            float* ch0_limit, float* ch1_limit, float* ch2_limit);

                                            
uint8_t Verify_Multi_Channel_Config_Data(multi_channel_config_t* data);
uint32_t Calculate_Multi_Channel_Config_Checksum(multi_channel_config_t* data);

/******************** 三通道 *******************/

/******************** Float数组存储区域 *******************/
// Float数组存储地址 (0x010000) - 专门存储10个float变量
#define FLOAT_ARRAY_STORAGE_ADDR   0x010000
#define FLOAT_ARRAY_MAGIC_NUMBER   0x464C5441  // "FLTA"

// Float数组存储结构体
typedef struct {
    uint32_t magic;           // 魔数验证
    float values[10];         // 10个float变量
    uint32_t checksum;        // 校验和
} float_storage_t;

/******************** Float数组存储函数 *******************/
uint8_t Save_Float_Array_To_Flash(float values[10]);
uint8_t Read_Float_Array_From_Flash(float values[10]);

/******************** 简化版本 - 单个float变量存储 *******************/
uint8_t Save_Single_Float(float value, uint8_t position);
uint8_t Load_Single_Float(float* value, uint8_t position);

/******************** Float数组存储区域结束 *******************/
#endif
