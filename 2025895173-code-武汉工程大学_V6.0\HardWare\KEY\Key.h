#ifndef __KEY_H__
#define __KEY_H__

#include "HeaderFiles.h"

#define KEY1_PORT GPIOD
#define KEY2_PORT GPIOD
#define KEY3_PORT GPIOD
#define KEY4_PORT GPIOD
#define KEY5_PORT GPIOD
#define KEY6_PORT GPIOD

#define KEY1_PIN GPIO_PIN_8
#define KEY2_PIN GPIO_PIN_9
#define KEY3_PIN GPIO_PIN_10
#define KEY4_PIN GPIO_PIN_11
#define KEY5_PIN GPIO_PIN_12
#define KEY6_PIN GPIO_PIN_13

// 按键状态位掩码定义
#define KEY1_MASK 0x01 
#define KEY2_MASK 0x02
#define KEY3_MASK 0x04
#define KEY4_MASK 0x08
#define KEY5_MASK 0x10
#define KEY6_MASK 0x20

void Key_Init(void);
uint8_t Key_Read(void);

#endif
