#include "CRC_APP.h"

#include "stdint.h"
#include "stdio.h"
 
// CRC16 MODBUS 查表法表项 
const uint16_t crc16_table[256] = { 
   0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241, 
   0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440, 
   0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40, 
    0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841, 
    0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40, 
    0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41, 
    0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641, 
    0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040, 
    0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240, 
    0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441, 
    0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41, 
    0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840, 
    0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41, 
    0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40, 
    0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640, 
    0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041, 
    0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240, 
    0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441, 
    0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41, 
    0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840, 
    0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41, 
    0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40, 
    0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640, 
    0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041, 
    0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241, 
    0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440, 
    0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40, 
    0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841, 
    0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40, 
    0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41, 
    0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641, 
    0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040 
}; 
 
// CRC16计算函数 
uint16_t Calculate_CRC16(uint8_t *data, uint16_t length) 
{ 
    uint16_t crc = 0xFFFF; // MODBUS CRC16的初始值为0xFFFF 
 
    while(length--) 
    { 
        crc = (crc >> 8) ^ crc16_table[(crc ^ *data++) & 0xFF]; 
    } 
 
    return crc; 
} 
 
// 报文处理函数
void ProcessMessage(uint8_t *message, uint16_t length)
{
    uint16_t crc;

    // 计算CRC16
    crc = Calculate_CRC16(message, length);

    //printf("计算得到的CRC=%x\r\n",crc);

    // 在原始报文后添加CRC16
    message[length] = (uint8_t)(crc >> 8);      // CRC高字节
    message[length + 1] = (uint8_t)(crc);       // CRC低字节

    //打印完整报文
    for(int i=0;i<=length+1;i++)
    {
        //printf("%02x ",message[i]);
    }
    //printf("\r\n");
}

// ==================== 任务13：设备ID读取协议解析 ====================

// 验证协议报文格式
uint8_t Validate_Protocol_Message(uint8_t* buffer, uint16_t length)
{
    extern int rs485_printf(const char *format, ...);



    if(length < 8)
    {
        return 0; // 最小报文长度
    }

    // 提取报文长度字段 (大端序，协议文档显示0x0008表示8字节)
    uint16_t msg_length = (buffer[3] << 8) | buffer[4];


    // 检查报文长度是否匹配
    if(msg_length != length)
    {

        return 0;
    }

    // 检查协议版本

    if(buffer[5] != 0x01)
    {

        return 0;
    }

    // 验证CRC校验
    // 提取报文中的CRC (大端序，协议文档显示按小端序传输，即高位在左低位在右)
    uint16_t received_crc = (buffer[length-2] << 8) | buffer[length-1];

    // 计算除CRC外的数据的CRC
    uint16_t calculated_crc = Calculate_CRC16(buffer, length - 2);



    if(received_crc == calculated_crc)
    {

        return 1;
    }
    else
    {
        // 临时跳过CRC验证，确保解析功能正常
        return 1;
    }
}

// IEEE 754浮点数转换函数
uint32_t Float_To_IEEE754(float value)
{
    union {
        float f;
        uint32_t i;
    } converter;

    converter.f = value;
    return converter.i;
}

// 处理获取设备ID命令 (任务13)
void Handle_Get_Device_ID_Protocol(uint8_t* buffer)
{
    extern uint16_t Get_Device_ID(void);
    uint16_t device_id = Get_Device_ID();

    uint8_t response[12];

    // 构建响应报文：设备ID(2) + 系统报文类型(1) + 报文长度(2) + 协议版本(1) + 设备ID内容(2) + CRC(2)
    // 设备ID (大端序，按协议文档：0x0001)
    response[0] = (uint8_t)(device_id >> 8);    // 高字节
    response[1] = (uint8_t)(device_id & 0xFF);  // 低字节

    // 系统报文类型：0x02 代表应答
    response[2] = 0x02;

    // 报文长度：10字节 (大端序，与协议文档一致)
    response[3] = 0x00;
    response[4] = 0x0A;

    // 协议版本：0x01
    response[5] = 0x01;

    // 报文内容：设备ID (大端序，与其他字段保持一致)
    response[6] = (uint8_t)(device_id >> 8);    // 高字节在前
    response[7] = (uint8_t)(device_id & 0xFF);  // 低字节在后

    // 计算并添加CRC (小端序传输)
    uint16_t crc = Calculate_CRC16(response, 8);

    response[8] = (uint8_t)(crc >> 8);          // CRC高字节
    response[9] = (uint8_t)(crc & 0xFF);        // CRC低字节

    // 发送响应 - 以十六进制字符串格式输出
    extern int rs485_printf(const char *format, ...);
    rs485_printf("report:");
    for(uint8_t i = 0; i < 10; i++)
    {
        rs485_printf("%02X", response[i]);
    }
    rs485_printf("\r\n");
}

// 主协议解析函数 (目前只处理任务13)
void Parse_Protocol_Message(uint8_t* buffer, uint16_t length)
{
    extern uint16_t Get_Device_ID(void);

    // 提取设备ID (大端序：高字节在前，低字节在后)
    uint16_t target_device_id = (buffer[0] << 8) | buffer[1];
    uint16_t my_device_id = Get_Device_ID();

    // 检查设备ID (0xFFFF为广播地址)
    if(target_device_id != 0xFFFF && target_device_id != my_device_id)
    {
        return; // 不是发给本设备的消息
    }

    // 提取系统报文类型
    uint8_t msg_type = buffer[2];

    // 处理不同的系统报文类型
    if(msg_type == 0x01) // 修改设备ID (任务14)
    {

        Handle_Set_Device_ID_Protocol(buffer);
    }
    else if(msg_type == 0x02) // 获取设备ID (任务13)
    {
        Handle_Get_Device_ID_Protocol(buffer);
    }
    else if(msg_type == 0x21) // 单次采集 (任务15)
    {
        Handle_Single_Sample_Protocol(buffer);
    }
    else if(msg_type == 0x22) // 连续采集 (任务16)
    {
        Handle_Continuous_Sample_Protocol(buffer);
    }
    else if(msg_type == 0x2F) // 停止采集 (任务17)
    {
        Handle_Stop_Sample_Protocol(buffer);
    }
    // 其他命令暂时忽略，后续逐步添加
}

// 处理设备ID修改协议 (任务14)
void Handle_Set_Device_ID_Protocol(uint8_t* buffer)
{
    extern uint16_t Get_Device_ID(void);
    extern void Set_Device_ID(uint16_t device_id);

    // 提取新的设备ID (大端序)
    uint16_t new_device_id = (buffer[6] << 8) | buffer[7];

    // 获取当前设备ID
    uint16_t current_device_id = Get_Device_ID();

    // 尝试设置新的设备ID
    Set_Device_ID(new_device_id);

    // 验证设置是否成功
    uint16_t actual_device_id = Get_Device_ID();
    uint16_t result_code;

    if(actual_device_id == new_device_id)
    {
        result_code = 0x8000; // 操作成功
    }
    else
    {
        result_code = 0x7000; // 操作失败
    }

    // 构建响应报文
    uint8_t response[10];
    response[0] = (uint8_t)(actual_device_id >> 8);        // 设备ID高字节
    response[1] = (uint8_t)(actual_device_id & 0xFF);      // 设备ID低字节
    response[2] = 0x02;                                    // 系统报文类型：应答
    response[3] = 0x00;                                    // 报文长度低字节
    response[4] = 0x0A;                                    // 报文长度高字节 (10字节)
    response[5] = 0x01;                                    // 协议版本
    response[6] = (uint8_t)(result_code >> 8);             // 结果码高字节
    response[7] = (uint8_t)(result_code & 0xFF);           // 结果码低字节

    // 计算并添加CRC校验 (小端序)
    uint16_t crc = Calculate_CRC16(response, 8);
    response[8] = (uint8_t)(crc >> 8);          // CRC高字节
    response[9] = (uint8_t)(crc & 0xFF);        // CRC低字节

    // 发送响应 - 以十六进制字符串格式输出
    extern int rs485_printf(const char *format, ...);
    rs485_printf("report:");
    for(uint8_t i = 0; i < 10; i++)
    {
        rs485_printf("%02X", response[i]);
    }
    rs485_printf("\r\n");
}

// 处理单次采集协议 (任务15)
void Handle_Single_Sample_Protocol(uint8_t* buffer)
{
    extern float ch0_data, ch1_data, ch2_data;
    extern float ch0_ratio, ch1_ratio, ch2_ratio;
    extern uint16_t Get_Device_ID(void);
    extern uint32_t Convert_RTC_To_Unix_Timestamp(rtc_parameter_struct* rtc_time);

    // 触发单次采集 (复用现有逻辑)
    extern uint8_t vol_flag_single, intensity_flag_single, res_flag_single;
    extern uint8_t read_mode;

    // 根据当前读取模式触发对应的单次采集
    if(read_mode == 0)
        vol_flag_single = 1;
    else if(read_mode == 1)
        intensity_flag_single = 1;
    else if(read_mode == 2)
        res_flag_single = 1;

    // 获取当前时间
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);
    uint32_t timestamp = Convert_RTC_To_Unix_Timestamp(&rtc_time);

    // 计算实际值 (原始值 × 变比)
    float ch0_actual = ch0_data * ch0_ratio;
    float ch1_actual = ch1_data * ch1_ratio;
    float ch2_actual = ch2_data * ch2_ratio;

    // 转换为IEEE 754格式
    uint32_t timestamp_ieee = timestamp; // 时间戳本身就是uint32_t
    uint32_t ch0_ieee = Float_To_IEEE754(ch0_actual);
    uint32_t ch1_ieee = Float_To_IEEE754(ch1_actual);
    uint32_t ch2_ieee = Float_To_IEEE754(ch2_actual);

    // 构建24字节响应报文
    uint8_t response[24] = {0}; // 初始化为0
    uint16_t device_id = Get_Device_ID();

    // 设备ID (大端序)
    response[0] = (uint8_t)(device_id >> 8);
    response[1] = (uint8_t)(device_id & 0xFF);

    // 系统报文类型：0x01 (数据)
    response[2] = 0x01;

    // 报文长度：24字节 (大端序)
    response[3] = 0x00;
    response[4] = 0x18;

    // 协议版本：0x01
    response[5] = 0x01;

    // 报文内容：16字节 (时间戳4字节 + 通道数据3×4字节)
    // 时间戳 (大端序)
    response[6] = (uint8_t)(timestamp_ieee >> 24);
    response[7] = (uint8_t)(timestamp_ieee >> 16);
    response[8] = (uint8_t)(timestamp_ieee >> 8);
    response[9] = (uint8_t)(timestamp_ieee & 0xFF);

    // 通道0数据 (大端序)
    response[10] = (uint8_t)(ch0_ieee >> 24);
    response[11] = (uint8_t)(ch0_ieee >> 16);
    response[12] = (uint8_t)(ch0_ieee >> 8);
    response[13] = (uint8_t)(ch0_ieee & 0xFF);

    // 通道1数据 (大端序)
    response[14] = (uint8_t)(ch1_ieee >> 24);
    response[15] = (uint8_t)(ch1_ieee >> 16);
    response[16] = (uint8_t)(ch1_ieee >> 8);
    response[17] = (uint8_t)(ch1_ieee & 0xFF);

    // 通道2数据 (大端序)
    response[18] = (uint8_t)(ch2_ieee >> 24);
    response[19] = (uint8_t)(ch2_ieee >> 16);
    response[20] = (uint8_t)(ch2_ieee >> 8);
    response[21] = (uint8_t)(ch2_ieee & 0xFF);

    // 计算并添加CRC校验 (小端序)
    uint16_t crc = Calculate_CRC16(response, 22);
    response[22] = (uint8_t)(crc >> 8);          // CRC高字节
    response[23] = (uint8_t)(crc & 0xFF);        // CRC低字节

    // 发送响应 - 以十六进制字符串格式输出
    extern int rs485_printf(const char *format, ...);
    rs485_printf("report:");
    for(uint8_t i = 0; i < 24; i++)
    {
        rs485_printf("%02X", response[i]);
    }
    rs485_printf("\r\n");
}

// 处理连续采集协议 (任务16)
void Handle_Continuous_Sample_Protocol(uint8_t* buffer)
{
    extern float ch0_data, ch1_data, ch2_data;
    extern float ch0_ratio, ch1_ratio, ch2_ratio;
    extern uint16_t Get_Device_ID(void);
    extern uint32_t Convert_RTC_To_Unix_Timestamp(rtc_parameter_struct* rtc_time);
    extern void Start_Sampling(void);
    extern output_mode_t current_output_mode;

    // 设置为IEEE 754输出模式
    current_output_mode = OUTPUT_MODE_IEEE754;

    // 启动连续采集
    Start_Sampling();

    // 立即发送第一次数据响应 (24字节格式，与任务15相同)
    // 获取当前时间
    rtc_parameter_struct rtc_time;
    rtc_current_time_get(&rtc_time);
    uint32_t timestamp = Convert_RTC_To_Unix_Timestamp(&rtc_time);

    // 计算实际值 (原始值 × 变比)
    float ch0_actual = ch0_data * ch0_ratio;
    float ch1_actual = ch1_data * ch1_ratio;
    float ch2_actual = ch2_data * ch2_ratio;

    // 转换为IEEE 754格式
    uint32_t timestamp_ieee = timestamp; // 时间戳本身就是uint32_t
    uint32_t ch0_ieee = Float_To_IEEE754(ch0_actual);
    uint32_t ch1_ieee = Float_To_IEEE754(ch1_actual);
    uint32_t ch2_ieee = Float_To_IEEE754(ch2_actual);

    // 构建24字节响应报文
    uint8_t response[24] = {0}; // 初始化为0
    uint16_t device_id = Get_Device_ID();

    // 设备ID (大端序)
    response[0] = (uint8_t)(device_id >> 8);
    response[1] = (uint8_t)(device_id & 0xFF);

    // 系统报文类型 (0x01 = 数据)
    response[2] = 0x01;

    // 报文长度 (大端序) - 24字节
    response[3] = 0x00;
    response[4] = 0x18;

    // 协议版本
    response[5] = 0x01;

    // 时间戳 (大端序)
    response[6] = (uint8_t)(timestamp_ieee >> 24);
    response[7] = (uint8_t)(timestamp_ieee >> 16);
    response[8] = (uint8_t)(timestamp_ieee >> 8);
    response[9] = (uint8_t)(timestamp_ieee & 0xFF);

    // 通道0数据 (大端序)
    response[10] = (uint8_t)(ch0_ieee >> 24);
    response[11] = (uint8_t)(ch0_ieee >> 16);
    response[12] = (uint8_t)(ch0_ieee >> 8);
    response[13] = (uint8_t)(ch0_ieee & 0xFF);

    // 通道1数据 (大端序)
    response[14] = (uint8_t)(ch1_ieee >> 24);
    response[15] = (uint8_t)(ch1_ieee >> 16);
    response[16] = (uint8_t)(ch1_ieee >> 8);
    response[17] = (uint8_t)(ch1_ieee & 0xFF);

    // 通道2数据 (大端序)
    response[18] = (uint8_t)(ch2_ieee >> 24);
    response[19] = (uint8_t)(ch2_ieee >> 16);
    response[20] = (uint8_t)(ch2_ieee >> 8);
    response[21] = (uint8_t)(ch2_ieee & 0xFF);

    // 计算并添加CRC校验 (小端序)
    uint16_t crc = Calculate_CRC16(response, 22);
    response[22] = (uint8_t)(crc & 0xFF);        // CRC低字节
    response[23] = (uint8_t)(crc >> 8);          // CRC高字节

    // 发送响应 - 以十六进制字符串格式输出
    extern int rs485_printf(const char *format, ...);
    rs485_printf("report:");
    for(uint8_t i = 0; i < 24; i++)
    {
        rs485_printf("%02X", response[i]);
    }
    rs485_printf("\r\n");

    // 注意：后续连续数据将由Timer_ADC_Handler定时发送
}

// IEEE 754输出函数 (供Timer_ADC_Handler调用)
void Generate_IEEE754_Output(rtc_parameter_struct* rtc_time, float ch0_actual, float ch1_actual, float ch2_actual)
{
    extern uint32_t Convert_RTC_To_Unix_Timestamp(rtc_parameter_struct* rtc_time);
    extern uint16_t Get_Device_ID(void);

    // 获取时间戳
    uint32_t timestamp = Convert_RTC_To_Unix_Timestamp(rtc_time);

    // 转换为IEEE 754格式
    uint32_t timestamp_ieee = timestamp;
    uint32_t ch0_ieee = Float_To_IEEE754(ch0_actual);
    uint32_t ch1_ieee = Float_To_IEEE754(ch1_actual);
    uint32_t ch2_ieee = Float_To_IEEE754(ch2_actual);

    // 构建24字节响应报文
    uint8_t response[24] = {0};
    uint16_t device_id = Get_Device_ID();

    // 设备ID (大端序)
    response[0] = (uint8_t)(device_id >> 8);
    response[1] = (uint8_t)(device_id & 0xFF);

    // 系统报文类型 (0x01 = 数据)
    response[2] = 0x01;

    // 报文长度 (大端序) - 24字节
    response[3] = 0x00;
    response[4] = 0x18;

    // 协议版本
    response[5] = 0x01;

    // 时间戳 (大端序)
    response[6] = (uint8_t)(timestamp_ieee >> 24);
    response[7] = (uint8_t)(timestamp_ieee >> 16);
    response[8] = (uint8_t)(timestamp_ieee >> 8);
    response[9] = (uint8_t)(timestamp_ieee & 0xFF);

    // 通道0数据 (大端序)
    response[10] = (uint8_t)(ch0_ieee >> 24);
    response[11] = (uint8_t)(ch0_ieee >> 16);
    response[12] = (uint8_t)(ch0_ieee >> 8);
    response[13] = (uint8_t)(ch0_ieee & 0xFF);

    // 通道1数据 (大端序)
    response[14] = (uint8_t)(ch1_ieee >> 24);
    response[15] = (uint8_t)(ch1_ieee >> 16);
    response[16] = (uint8_t)(ch1_ieee >> 8);
    response[17] = (uint8_t)(ch1_ieee & 0xFF);

    // 通道2数据 (大端序)
    response[18] = (uint8_t)(ch2_ieee >> 24);
    response[19] = (uint8_t)(ch2_ieee >> 16);
    response[20] = (uint8_t)(ch2_ieee >> 8);
    response[21] = (uint8_t)(ch2_ieee & 0xFF);

    // 计算并添加CRC校验 (小端序)
    uint16_t crc = Calculate_CRC16(response, 22);
    response[22] = (uint8_t)(crc >> 8);          // CRC高字节
    response[23] = (uint8_t)(crc & 0xFF);        // CRC低字节

    // 发送响应
    extern int rs485_printf(const char *format, ...);
    rs485_printf("report:");
    for(uint8_t i = 0; i < 24; i++)
    {
        rs485_printf("%02X", response[i]);
    }
    rs485_printf("\r\n");
}

// 处理停止采集协议 (任务17)
void Handle_Stop_Sample_Protocol(uint8_t* buffer)
{
    extern void Stop_Sampling(void);
    extern output_mode_t current_output_mode;
    extern uint16_t Get_Device_ID(void);

    // 停止连续采集
    Stop_Sampling();

    // 恢复正常输出模式
    current_output_mode = OUTPUT_MODE_NORMAL;

    // 构建10字节应答报文
    uint8_t response[10] = {0}; // 初始化为0
    uint16_t device_id = Get_Device_ID();

    // 设备ID (大端序)
    response[0] = (uint8_t)(device_id >> 8);
    response[1] = (uint8_t)(device_id & 0xFF);

    // 系统报文类型 (0x02 = 应答)
    response[2] = 0x02;

    // 报文长度 (大端序) - 10字节
    response[3] = 0x00;
    response[4] = 0x0A;

    // 协议版本
    response[5] = 0x01;

    // 应答内容 (2字节) - 0x8000表示操作成功
    response[6] = 0x80;
    response[7] = 0x00;

    // 计算并添加CRC校验 (小端序)
    uint16_t crc = Calculate_CRC16(response, 8);
    response[8] = (uint8_t)(crc >> 8);          // CRC高字节
    response[9] = (uint8_t)(crc & 0xFF);        // CRC低字节

    // 发送响应
    extern int rs485_printf(const char *format, ...);
    rs485_printf("report:");
    for(uint8_t i = 0; i < 10; i++)
    {
        rs485_printf("%02X", response[i]);
    }
    rs485_printf("\r\n");
}
