#include "Function.h"
#include "Sdcard_APP.h"

uint32_t uwTick;

void System_Init(void)
{
	systick_config();
}

void UsrFunction(void)
{
	OLED_Init();
	OLED_App_Init();
	Key_Init();
	Led_Init();
	//usart0_config();
	rs485_usart1_config();
	gd30ad3344_init();
	ADC_Init(); 
	scheduler_init();
	RTC_Init(); 
	Flash_Init();
	Init_Data_Recording();
	Create_Default_Config_INI();
	Write_Log_Data("system init");
	delay_1ms(500);//等待系统稳定
	Timer_Init();
	while(1)
	{
		scheduler_run();
	}
}
