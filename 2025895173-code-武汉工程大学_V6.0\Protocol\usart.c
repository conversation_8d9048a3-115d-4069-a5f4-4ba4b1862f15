#include "usart.h"
#include "HeaderFiles.h"
#include "gd32f4xx_usart.h"
#include <stdarg.h>

uint32_t uart_rx_ticks;
uint8_t uart_rx_index;
uint8_t uart_rx_buffer[128] = {0};
uint8_t uart_rx_idle_flag = 0;

uint32_t rs485_rx_ticks;
uint8_t rs485_rx_index;
uint8_t rs485_rx_buffer[128] = {0};
uint8_t rs485_rx_idle_flag = 0;


void usart0_config(void)
{
	// 使能外设时钟
	rcu_periph_clock_enable(RCU_GPIOA);
	rcu_periph_clock_enable(RCU_USART0);
	
	// 配置GPIO复用功能
	gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_9 | GPIO_PIN_10);
	//PA9 - TX
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_9);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_9);
	//PA10 - RX
	gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_10);
	gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_10);
	
	// 配置串口
	usart_deinit(USART0);
	usart_baudrate_set(USART0, 115200);
	usart_parity_config(USART0, USART_PM_NONE);
	usart_word_length_set(USART0, USART_WL_8BIT);
	usart_stop_bit_set(USART0, USART_STB_1BIT);
	usart_receive_config(USART0, USART_RECEIVE_ENABLE);
	usart_transmit_config(USART0, USART_TRANSMIT_ENABLE);
	usart_hardware_flow_cts_config(USART0, USART_CTS_DISABLE);
	usart_hardware_flow_rts_config(USART0, USART_RTS_DISABLE);
	
	// 使能串口中断
	usart_interrupt_enable(USART0, USART_INT_IDLE);
	usart_interrupt_enable(USART0, USART_INT_RBNE);
	nvic_irq_enable(USART0_IRQn, 0, 0);
	
	// 使能USART0
	usart_enable(USART0);

	// 初始化变量
	uart_rx_index = 0;
	memset(uart_rx_buffer, 0, sizeof(uart_rx_buffer));
}

void rs485_usart1_config(void)
{
    // 使能外设时钟
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_USART1);
    rcu_periph_clock_enable(RS485_DIR_CLK);
    
    // 配置RS485方向控制引脚
    gpio_mode_set(RS485_DIR_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN, RS485_DIR_PIN);
    gpio_output_options_set(RS485_DIR_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, RS485_DIR_PIN);
    RS485_RX_ENABLE;  // 默认为接收模式
    
    // 配置GPIO复用功能 - PA2(TX), PA3(RX)
    gpio_af_set(GPIOA, GPIO_AF_7, GPIO_PIN_2 | GPIO_PIN_3);
    // PA2 - TX
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_2);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_2);
    // PA3 - RX
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_3);
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3);
    
    // 配置串口
    usart_deinit(USART1);
    usart_baudrate_set(USART1, 115200);
    usart_parity_config(USART1, USART_PM_NONE);
    usart_word_length_set(USART1, USART_WL_8BIT);
    usart_stop_bit_set(USART1, USART_STB_1BIT);
    usart_receive_config(USART1, USART_RECEIVE_ENABLE);
    usart_transmit_config(USART1, USART_TRANSMIT_ENABLE);
    usart_hardware_flow_cts_config(USART1, USART_CTS_DISABLE);
    usart_hardware_flow_rts_config(USART1, USART_RTS_DISABLE);
    
    // 只使能接收相关中断，不使能发送完成中断
    usart_interrupt_enable(USART1, USART_INT_IDLE);
    usart_interrupt_enable(USART1, USART_INT_RBNE);
    nvic_irq_enable(USART1_IRQn, 0, 0);
    
    // 使能USART1
    usart_enable(USART1);

    // 初始化变量
    rs485_rx_index = 0;
    memset(rs485_rx_buffer, 0, sizeof(rs485_rx_buffer));
}


// 串口0重定向
int fputc(int ch, FILE *f)
{
	usart_data_transmit(USART0, (uint8_t)ch);
	while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
	return ch;
}

int rs485_printf(const char *format, ...)
{
    va_list args;
    int length;
    static char buffer[256];
    
    va_start(args, format);
    length = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    // 切换为发送模式
    RS485_TX_ENABLE;
    
    // // 短暂延时确保切换完成
    for(volatile uint32_t i = 0; i < 100; i++);
    
    // 发送数据
    for(int i = 0; i < length; i++) {
        usart_data_transmit(USART1, (uint8_t)buffer[i]);
        while(RESET == usart_flag_get(USART1, USART_FLAG_TBE));
    }
    
    // 等待发送完成
    while(RESET == usart_flag_get(USART1, USART_FLAG_TC));
    
    // 切换为接收模式
    RS485_RX_ENABLE;
    
    return length;
}

// USART0中断处理函数
void USART0_IRQHandler(void)
{
	// 移除调试输出，保持中断处理简洁

	// 检测IDLE中断 - 一帧数据接收完成
	if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_IDLE))
	{
		// 清除IDLE标志
		usart_data_receive(USART0);

		// 设置IDLE标志，表示一帧数据接收完成
		uart_rx_idle_flag = 1;

		// 更新时间戳
		uart_rx_ticks = uwTick;
	}

	// 检测RBNE中断 - 接收数据寄存器非空
	if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_RBNE))
	{
		uint8_t data = usart_data_receive(USART0);
		if(uart_rx_index < sizeof(uart_rx_buffer) - 1)
			uart_rx_buffer[uart_rx_index++] = data;

		usart_interrupt_flag_clear(USART0, USART_INT_FLAG_RBNE);
		uart_rx_ticks = uwTick;
	}
	
	// 处理错误中断
	if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_ERR_ORERR) ||
	   RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_ERR_NERR) ||
	   RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_ERR_FERR))
	{
		usart_data_receive(USART0); // 清除错误标志
	}
}


// USART1中断处理函数
// USART1中断处理函数 - 移除发送完成中断处理
void USART1_IRQHandler(void)
{
    // 检测IDLE中断 - 一帧数据接收完成
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE))
    {
        // 清除IDLE标志
        usart_data_receive(USART1);

        // 设置IDLE标志，表示一帧数据接收完成
        rs485_rx_idle_flag = 1;

        // 更新时间戳
        rs485_rx_ticks = uwTick;
    }

    // 检测RBNE中断 - 接收数据寄存器非空
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_RBNE))
    {
        uint8_t data = usart_data_receive(USART1);
        if(rs485_rx_index < sizeof(rs485_rx_buffer) - 1)
            rs485_rx_buffer[rs485_rx_index++] = data;

        usart_interrupt_flag_clear(USART1, USART_INT_FLAG_RBNE);
        rs485_rx_ticks = uwTick;
    }
    
    // 处理错误中断
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_ERR_ORERR) ||
       RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_ERR_NERR) ||
       RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_ERR_FERR))
    {
        usart_data_receive(USART1); // 清除错误标志
    }
}

// RS485发送二进制数据函数
void rs485_send_data(uint8_t* data, uint16_t length)
{
    if(data == NULL || length == 0) return;

    // 设置为发送模式
    RS485_TX_ENABLE;
    delay_1ms(1); // 短暂延时确保方向切换

    // 发送数据
    for(uint16_t i = 0; i < length; i++)
    {
        while(RESET == usart_flag_get(USART1, USART_FLAG_TBE));
        usart_data_transmit(USART1, data[i]);
    }

    // 等待发送完成
    while(RESET == usart_flag_get(USART1, USART_FLAG_TC));

    delay_1ms(1); // 短暂延时确保数据发送完成
    // 切换回接收模式
    RS485_RX_ENABLE;
}
