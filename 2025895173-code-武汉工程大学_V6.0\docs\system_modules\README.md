# 系统模块详细文档

本目录包含武汉工程大学嵌入式项目各个独立系统模块的详细说明文档。

## 文档结构

每个系统模块文档包含以下内容：
- **模块概述**: 模块功能和作用
- **核心数据结构**: 重要的结构体和枚举定义
- **函数详细说明**: 每个函数的用法、参数、返回值和功能描述
- **使用示例**: 典型的调用示例
- **注意事项**: 使用时需要注意的问题
- **二次开发指南**: 如何基于现有代码进行扩展

## 模块列表

1. **SD卡系统** (`sdcard_system.md`) - SD卡文件操作和数据存储
2. **Flash系统** (`flash_system.md`) - 内部Flash和外部SPI Flash管理
3. **RTC系统** (`rtc_system.md`) - 实时时钟和时间管理
4. **ADC系统** (`adc_system.md`) - 模拟数字转换和数据采集
5. **OLED显示系统** (`oled_system.md`) - OLED显示屏控制和界面管理
6. **按键系统** (`key_system.md`) - 按键输入处理和事件管理
7. **串口通信系统** (`usart_system.md`) - 串口通信和协议解析
8. **定时器系统** (`timer_system.md`) - 定时器配置和任务调度
9. **CRC校验系统** (`crc_system.md`) - 数据校验和完整性验证

## 使用说明

1. 每个文档都是独立的，可以单独阅读
2. 建议按照系统启动顺序阅读相关文档
3. 所有示例代码都基于实际项目代码
4. 文档中的函数说明与源代码保持同步

## 更新记录

- 2025-01-XX: 初始版本创建
- 后续更新将在此记录
