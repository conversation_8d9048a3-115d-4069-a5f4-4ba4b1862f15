


ARM Macro Assembler    Page 1 


    1 00000000         ;/*!
    2 00000000         ;    \file    startup_gd32f450_470.s
    3 00000000         ;    \brief   start up file
    4 00000000         ;
    5 00000000         ;    \version 2024-01-15, V3.2.0, firmware for GD32F4xx
    6 00000000         ;*/
    7 00000000         ;
    8 00000000         ;/*  Copyright (c) 2012 ARM LIMITED
    9 00000000         ;    Copyright (c) 2024, GigaDevice Semiconductor Inc.
   10 00000000         ;
   11 00000000         ;   All rights reserved.
   12 00000000         ;   Redistribution and use in source and binary forms, w
                       ith or without
   13 00000000         ;   modification, are permitted provided that the follow
                       ing conditions are met:
   14 00000000         ;   - Redistributions of source code must retain the abo
                       ve copyright
   15 00000000         ;     notice, this list of conditions and the following 
                       disclaimer.
   16 00000000         ;   - Redistributions in binary form must reproduce the 
                       above copyright
   17 00000000         ;     notice, this list of conditions and the following 
                       disclaimer in the
   18 00000000         ;     documentation and/or other materials provided with
                        the distribution.
   19 00000000         ;   - Neither the name of ARM nor the names of its contr
                       ibutors may be used
   20 00000000         ;     to endorse or promote products derived from this s
                       oftware without
   21 00000000         ;     specific prior written permission.
   22 00000000         ;   *
   23 00000000         ;   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS A
                       ND CONTRIBUTORS "AS IS"
   24 00000000         ;   AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BU
                       T NOT LIMITED TO, THE
   25 00000000         ;   IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FO
                       R A PARTICULAR PURPOSE
   26 00000000         ;   ARE DISCLAIMED. IN NO EVENT SHALL COPYRIGHT HOLDERS 
                       AND CONTRIBUTORS BE
   27 00000000         ;   LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL
                       , EXEMPLARY, OR
   28 00000000         ;   CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO
                       , PROCUREMENT OF
   29 00000000         ;   SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
                       PROFITS; OR BUSINESS
   30 00000000         ;   INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LI
                       ABILITY, WHETHER IN
   31 00000000         ;   CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLI
                       GENCE OR OTHERWISE)
   32 00000000         ;   ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, 
                       EVEN IF ADVISED OF THE
   33 00000000         ;   POSSIBILITY OF SUCH DAMAGE.
   34 00000000         ;   ----------------------------------------------------
                       -----------------------*/
   35 00000000         
   36 00000000         ;/* This file refers the CMSIS standard, some adjustment
                       s are made according to GigaDevice chips */
   37 00000000         
   38 00000000         ; <h> Stack Configuration



ARM Macro Assembler    Page 2 


   39 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   40 00000000         ; </h>
   41 00000000         
   42 00000000 00000800 
                       Stack_Size
                               EQU              0x00000800
   43 00000000         
   44 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   45 00000000         Stack_Mem
                               SPACE            Stack_Size
   46 00000800         __initial_sp
   47 00000800         
   48 00000800         
   49 00000800         ; <h> Heap Configuration
   50 00000800         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   51 00000800         ; </h>
   52 00000800         
   53 00000800 00000800 
                       Heap_Size
                               EQU              0x00000800
   54 00000800         
   55 00000800                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   56 00000000         __heap_base
   57 00000000         Heap_Mem
                               SPACE            Heap_Size
   58 00000800         __heap_limit
   59 00000800         
   60 00000800                 PRESERVE8
   61 00000800                 THUMB
   62 00000800         
   63 00000800         ;               /* reset Vector Mapped to at Address 0 *
                       /
   64 00000800                 AREA             RESET, DATA, READONLY
   65 00000000                 EXPORT           __Vectors
   66 00000000                 EXPORT           __Vectors_End
   67 00000000                 EXPORT           __Vectors_Size
   68 00000000         
   69 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   70 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   71 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   72 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   73 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   74 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   75 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   76 0000001C 00000000        DCD              0           ; Reserved
   77 00000020 00000000        DCD              0           ; Reserved
   78 00000024 00000000        DCD              0           ; Reserved
   79 00000028 00000000        DCD              0           ; Reserved
   80 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler



ARM Macro Assembler    Page 3 


   81 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   82 00000034 00000000        DCD              0           ; Reserved
   83 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   84 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   85 00000040         
   86 00000040         ;               /* external interrupts handler */
   87 00000040 00000000        DCD              WWDGT_IRQHandler ; 16:Window Wa
                                                            tchdog Timer
   88 00000044 00000000        DCD              LVD_IRQHandler ; 17:LVD through
                                                             EXTI Line detect
   89 00000048 00000000        DCD              TAMPER_STAMP_IRQHandler ; 18:Ta
                                                            mper and TimeStamp 
                                                            through EXTI Line d
                                                            etect
   90 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; 19:RTC Wa
                                                            keup through EXTI L
                                                            ine
   91 00000050 00000000        DCD              FMC_IRQHandler ; 20:FMC
   92 00000054 00000000        DCD              RCU_CTC_IRQHandler 
                                                            ; 21:RCU and CTC
   93 00000058 00000000        DCD              EXTI0_IRQHandler 
                                                            ; 22:EXTI Line 0
   94 0000005C 00000000        DCD              EXTI1_IRQHandler 
                                                            ; 23:EXTI Line 1
   95 00000060 00000000        DCD              EXTI2_IRQHandler 
                                                            ; 24:EXTI Line 2
   96 00000064 00000000        DCD              EXTI3_IRQHandler 
                                                            ; 25:EXTI Line 3
   97 00000068 00000000        DCD              EXTI4_IRQHandler 
                                                            ; 26:EXTI Line 4
   98 0000006C 00000000        DCD              DMA0_Channel0_IRQHandler 
                                                            ; 27:DMA0 Channel0
   99 00000070 00000000        DCD              DMA0_Channel1_IRQHandler 
                                                            ; 28:DMA0 Channel1
  100 00000074 00000000        DCD              DMA0_Channel2_IRQHandler 
                                                            ; 29:DMA0 Channel2
  101 00000078 00000000        DCD              DMA0_Channel3_IRQHandler 
                                                            ; 30:DMA0 Channel3
  102 0000007C 00000000        DCD              DMA0_Channel4_IRQHandler 
                                                            ; 31:DMA0 Channel4
  103 00000080 00000000        DCD              DMA0_Channel5_IRQHandler 
                                                            ; 32:DMA0 Channel5
  104 00000084 00000000        DCD              DMA0_Channel6_IRQHandler 
                                                            ; 33:DMA0 Channel6
  105 00000088 00000000        DCD              ADC_IRQHandler ; 34:ADC
  106 0000008C 00000000        DCD              CAN0_TX_IRQHandler ; 35:CAN0 TX
                                                            
  107 00000090 00000000        DCD              CAN0_RX0_IRQHandler 
                                                            ; 36:CAN0 RX0
  108 00000094 00000000        DCD              CAN0_RX1_IRQHandler 
                                                            ; 37:CAN0 RX1
  109 00000098 00000000        DCD              CAN0_EWMC_IRQHandler 
                                                            ; 38:CAN0 EWMC
  110 0000009C 00000000        DCD              EXTI5_9_IRQHandler 
                                                            ; 39:EXTI5 to EXTI9
                                                            



ARM Macro Assembler    Page 4 


  111 000000A0 00000000        DCD              TIMER0_BRK_TIMER8_IRQHandler ; 
                                                            40:TIMER0 Break and
                                                             TIMER8
  112 000000A4 00000000        DCD              TIMER0_UP_TIMER9_IRQHandler ; 4
                                                            1:TIMER0 Update and
                                                             TIMER9
  113 000000A8 00000000        DCD              TIMER0_TRG_CMT_TIMER10_IRQHandl
er 
                                                            ; 42:TIMER0 Trigger
                                                             and Commutation an
                                                            d TIMER10
  114 000000AC 00000000        DCD              TIMER0_Channel_IRQHandler ; 43:
                                                            TIMER0 Capture Comp
                                                            are
  115 000000B0 00000000        DCD              TIMER1_IRQHandler ; 44:TIMER1
  116 000000B4 00000000        DCD              TIMER2_IRQHandler ; 45:TIMER2
  117 000000B8 00000000        DCD              TIMER3_IRQHandler ; 46:TIMER3
  118 000000BC 00000000        DCD              I2C0_EV_IRQHandler 
                                                            ; 47:I2C0 Event
  119 000000C0 00000000        DCD              I2C0_ER_IRQHandler 
                                                            ; 48:I2C0 Error
  120 000000C4 00000000        DCD              I2C1_EV_IRQHandler 
                                                            ; 49:I2C1 Event
  121 000000C8 00000000        DCD              I2C1_ER_IRQHandler 
                                                            ; 50:I2C1 Error
  122 000000CC 00000000        DCD              SPI0_IRQHandler ; 51:SPI0
  123 000000D0 00000000        DCD              SPI1_IRQHandler ; 52:SPI1
  124 000000D4 00000000        DCD              USART0_IRQHandler ; 53:USART0
  125 000000D8 00000000        DCD              USART1_IRQHandler ; 54:USART1
  126 000000DC 00000000        DCD              USART2_IRQHandler ; 55:USART2
  127 000000E0 00000000        DCD              EXTI10_15_IRQHandler ; 56:EXTI1
                                                            0 to EXTI15
  128 000000E4 00000000        DCD              RTC_Alarm_IRQHandler 
                                                            ; 57:RTC Alarm
  129 000000E8 00000000        DCD              USBFS_WKUP_IRQHandler 
                                                            ; 58:USBFS Wakeup
  130 000000EC 00000000        DCD              TIMER7_BRK_TIMER11_IRQHandler ;
                                                             59:TIMER7 Break an
                                                            d TIMER11
  131 000000F0 00000000        DCD              TIMER7_UP_TIMER12_IRQHandler ; 
                                                            60:TIMER7 Update an
                                                            d TIMER12
  132 000000F4 00000000        DCD              TIMER7_TRG_CMT_TIMER13_IRQHandl
er 
                                                            ; 61:TIMER7 Trigger
                                                             and Commutation an
                                                            d TIMER13
  133 000000F8 00000000        DCD              TIMER7_Channel_IRQHandler ; 62:
                                                            TIMER7 Channel Capt
                                                            ure Compare
  134 000000FC 00000000        DCD              DMA0_Channel7_IRQHandler 
                                                            ; 63:DMA0 Channel7
  135 00000100 00000000        DCD              EXMC_IRQHandler ; 64:EXMC
  136 00000104 00000000        DCD              SDIO_IRQHandler ; 65:SDIO
  137 00000108 00000000        DCD              TIMER4_IRQHandler ; 66:TIMER4
  138 0000010C 00000000        DCD              SPI2_IRQHandler ; 67:SPI2
  139 00000110 00000000        DCD              UART3_IRQHandler ; 68:UART3
  140 00000114 00000000        DCD              UART4_IRQHandler ; 69:UART4
  141 00000118 00000000        DCD              TIMER5_DAC_IRQHandler ; 70:TIME



ARM Macro Assembler    Page 5 


                                                            R5 and DAC0 DAC1 Un
                                                            derrun error
  142 0000011C 00000000        DCD              TIMER6_IRQHandler ; 71:TIMER6
  143 00000120 00000000        DCD              DMA1_Channel0_IRQHandler 
                                                            ; 72:DMA1 Channel0
  144 00000124 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; 73:DMA1 Channel1
  145 00000128 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; 74:DMA1 Channel2
  146 0000012C 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; 75:DMA1 Channel3
  147 00000130 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; 76:DMA1 Channel4
  148 00000134 00000000        DCD              ENET_IRQHandler ; 77:Ethernet
  149 00000138 00000000        DCD              ENET_WKUP_IRQHandler ; 78:Ether
                                                            net Wakeup through 
                                                            EXTI Line
  150 0000013C 00000000        DCD              CAN1_TX_IRQHandler ; 79:CAN1 TX
                                                            
  151 00000140 00000000        DCD              CAN1_RX0_IRQHandler 
                                                            ; 80:CAN1 RX0
  152 00000144 00000000        DCD              CAN1_RX1_IRQHandler 
                                                            ; 81:CAN1 RX1
  153 00000148 00000000        DCD              CAN1_EWMC_IRQHandler 
                                                            ; 82:CAN1 EWMC
  154 0000014C 00000000        DCD              USBFS_IRQHandler ; 83:USBFS
  155 00000150 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; 84:DMA1 Channel5
  156 00000154 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; 85:DMA1 Channel6
  157 00000158 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; 86:DMA1 Channel7
  158 0000015C 00000000        DCD              USART5_IRQHandler ; 87:USART5
  159 00000160 00000000        DCD              I2C2_EV_IRQHandler 
                                                            ; 88:I2C2 Event
  160 00000164 00000000        DCD              I2C2_ER_IRQHandler 
                                                            ; 89:I2C2 Error
  161 00000168 00000000        DCD              USBHS_EP1_Out_IRQHandler ; 90:U
                                                            SBHS Endpoint 1 Out
                                                             
  162 0000016C 00000000        DCD              USBHS_EP1_In_IRQHandler ; 91:US
                                                            BHS Endpoint 1 in
  163 00000170 00000000        DCD              USBHS_WKUP_IRQHandler ; 92:USBH
                                                            S Wakeup through EX
                                                            TI Line
  164 00000174 00000000        DCD              USBHS_IRQHandler ; 93:USBHS
  165 00000178 00000000        DCD              DCI_IRQHandler ; 94:DCI
  166 0000017C 00000000        DCD              0           ; 95:Reserved
  167 00000180 00000000        DCD              TRNG_IRQHandler ; 96:TRNG
  168 00000184 00000000        DCD              FPU_IRQHandler ; 97:FPU
  169 00000188 00000000        DCD              UART6_IRQHandler ; 98:UART6
  170 0000018C 00000000        DCD              UART7_IRQHandler ; 99:UART7
  171 00000190 00000000        DCD              SPI3_IRQHandler ; 100:SPI3
  172 00000194 00000000        DCD              SPI4_IRQHandler ; 101:SPI4
  173 00000198 00000000        DCD              SPI5_IRQHandler ; 102:SPI5
  174 0000019C 00000000        DCD              0           ; 103:Reserved
  175 000001A0 00000000        DCD              TLI_IRQHandler ; 104:TLI
  176 000001A4 00000000        DCD              TLI_ER_IRQHandler 
                                                            ; 105:TLI Error



ARM Macro Assembler    Page 6 


  177 000001A8 00000000        DCD              IPA_IRQHandler ; 106:IPA
  178 000001AC         
  179 000001AC         __Vectors_End
  180 000001AC         
  181 000001AC 000001AC 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  182 000001AC         
  183 000001AC                 AREA             |.text|, CODE, READONLY
  184 00000000         
  185 00000000         ;/* reset Handler */
  186 00000000         Reset_Handler
                               PROC
  187 00000000                 EXPORT           Reset_Handler                  
   [WEAK]
  188 00000000                 IMPORT           SystemInit
  189 00000000                 IMPORT           __main
  190 00000000 4806            LDR              R0, =SystemInit
  191 00000002 4780            BLX              R0
  192 00000004 4806            LDR              R0, =__main
  193 00000006 4700            BX               R0
  194 00000008                 ENDP
  195 00000008         
  196 00000008         ;/* dummy Exception Handlers */
  197 00000008         NMI_Handler
                               PROC
  198 00000008                 EXPORT           NMI_Handler                    
   [WEAK]
  199 00000008 E7FE            B                .
  200 0000000A                 ENDP
  202 0000000A         HardFault_Handler
                               PROC
  203 0000000A                 EXPORT           HardFault_Handler              
   [WEAK]
  204 0000000A E7FE            B                .
  205 0000000C                 ENDP
  207 0000000C         MemManage_Handler
                               PROC
  208 0000000C                 EXPORT           MemManage_Handler              
   [WEAK]
  209 0000000C E7FE            B                .
  210 0000000E                 ENDP
  212 0000000E         BusFault_Handler
                               PROC
  213 0000000E                 EXPORT           BusFault_Handler               
   [WEAK]
  214 0000000E E7FE            B                .
  215 00000010                 ENDP
  217 00000010         UsageFault_Handler
                               PROC
  218 00000010                 EXPORT           UsageFault_Handler             
   [WEAK]
  219 00000010 E7FE            B                .
  220 00000012                 ENDP
  221 00000012         SVC_Handler
                               PROC
  222 00000012                 EXPORT           SVC_Handler                    
   [WEAK]
  223 00000012 E7FE            B                .



ARM Macro Assembler    Page 7 


  224 00000014                 ENDP
  226 00000014         DebugMon_Handler
                               PROC
  227 00000014                 EXPORT           DebugMon_Handler               
   [WEAK]
  228 00000014 E7FE            B                .
  229 00000016                 ENDP
  231 00000016         PendSV_Handler
                               PROC
  232 00000016                 EXPORT           PendSV_Handler                 
   [WEAK]
  233 00000016 E7FE            B                .
  234 00000018                 ENDP
  236 00000018         SysTick_Handler
                               PROC
  237 00000018                 EXPORT           SysTick_Handler                
   [WEAK]
  238 00000018 E7FE            B                .
  239 0000001A                 ENDP
  240 0000001A         
  241 0000001A         Default_Handler
                               PROC
  242 0000001A         ;               /* external interrupts handler */
  243 0000001A                 EXPORT           WWDGT_IRQHandler               
   [WEAK]
  244 0000001A                 EXPORT           LVD_IRQHandler                 
   [WEAK]
  245 0000001A                 EXPORT           TAMPER_STAMP_IRQHandler        
   [WEAK]
  246 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  247 0000001A                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  248 0000001A                 EXPORT           RCU_CTC_IRQHandler             
   [WEAK]
  249 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  250 0000001A                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  251 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  252 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  253 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  254 0000001A                 EXPORT           DMA0_Channel0_IRQHandler       
   [WEAK]
  255 0000001A                 EXPORT           DMA0_Channel1_IRQHandler       
   [WEAK]
  256 0000001A                 EXPORT           DMA0_Channel2_IRQHandler       
   [WEAK]
  257 0000001A                 EXPORT           DMA0_Channel3_IRQHandler       
   [WEAK]
  258 0000001A                 EXPORT           DMA0_Channel4_IRQHandler       
   [WEAK]
  259 0000001A                 EXPORT           DMA0_Channel5_IRQHandler       
   [WEAK]
  260 0000001A                 EXPORT           DMA0_Channel6_IRQHandler       
   [WEAK]



ARM Macro Assembler    Page 8 


  261 0000001A                 EXPORT           ADC_IRQHandler                 
   [WEAK]
  262 0000001A                 EXPORT           CAN0_TX_IRQHandler             
   [WEAK]
  263 0000001A                 EXPORT           CAN0_RX0_IRQHandler            
   [WEAK]
  264 0000001A                 EXPORT           CAN0_RX1_IRQHandler            
   [WEAK]
  265 0000001A                 EXPORT           CAN0_EWMC_IRQHandler           
   [WEAK]
  266 0000001A                 EXPORT           EXTI5_9_IRQHandler             
   [WEAK]
  267 0000001A                 EXPORT           TIMER0_BRK_TIMER8_IRQHandler   
   [WEAK]
  268 0000001A                 EXPORT           TIMER0_UP_TIMER9_IRQHandler    
   [WEAK]
  269 0000001A                 EXPORT           TIMER0_TRG_CMT_TIMER10_IRQHandl
er [WEAK]
  270 0000001A                 EXPORT           TIMER0_Channel_IRQHandler      
   [WEAK]
  271 0000001A                 EXPORT           TIMER1_IRQHandler              
   [WEAK]
  272 0000001A                 EXPORT           TIMER2_IRQHandler              
   [WEAK]
  273 0000001A                 EXPORT           TIMER3_IRQHandler              
   [WEAK]
  274 0000001A                 EXPORT           I2C0_EV_IRQHandler             
   [WEAK]
  275 0000001A                 EXPORT           I2C0_ER_IRQHandler             
   [WEAK]
  276 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  277 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  278 0000001A                 EXPORT           SPI0_IRQHandler                
   [WEAK]
  279 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  280 0000001A                 EXPORT           USART0_IRQHandler              
   [WEAK]
  281 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  282 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  283 0000001A                 EXPORT           EXTI10_15_IRQHandler           
   [WEAK]
  284 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  285 0000001A                 EXPORT           USBFS_WKUP_IRQHandler          
   [WEAK]
  286 0000001A                 EXPORT           TIMER7_BRK_TIMER11_IRQHandler  
   [WEAK]
  287 0000001A                 EXPORT           TIMER7_UP_TIMER12_IRQHandler   
   [WEAK]
  288 0000001A                 EXPORT           TIMER7_TRG_CMT_TIMER13_IRQHandl
er [WEAK]
  289 0000001A                 EXPORT           TIMER7_Channel_IRQHandler      
   [WEAK]
  290 0000001A                 EXPORT           DMA0_Channel7_IRQHandler       



ARM Macro Assembler    Page 9 


   [WEAK]
  291 0000001A                 EXPORT           EXMC_IRQHandler                
   [WEAK]
  292 0000001A                 EXPORT           SDIO_IRQHandler                
   [WEAK]
  293 0000001A                 EXPORT           TIMER4_IRQHandler              
   [WEAK]
  294 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  295 0000001A                 EXPORT           UART3_IRQHandler               
   [WEAK]
  296 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  297 0000001A                 EXPORT           TIMER5_DAC_IRQHandler          
   [WEAK]
  298 0000001A                 EXPORT           TIMER6_IRQHandler              
   [WEAK]
  299 0000001A                 EXPORT           DMA1_Channel0_IRQHandler       
   [WEAK]
  300 0000001A                 EXPORT           DMA1_Channel1_IRQHandler       
   [WEAK]
  301 0000001A                 EXPORT           DMA1_Channel2_IRQHandler       
   [WEAK]
  302 0000001A                 EXPORT           DMA1_Channel3_IRQHandler       
   [WEAK]
  303 0000001A                 EXPORT           DMA1_Channel4_IRQHandler       
   [WEAK]
  304 0000001A                 EXPORT           ENET_IRQHandler                
   [WEAK]
  305 0000001A                 EXPORT           ENET_WKUP_IRQHandler           
   [WEAK]
  306 0000001A                 EXPORT           CAN1_TX_IRQHandler             
   [WEAK]
  307 0000001A                 EXPORT           CAN1_RX0_IRQHandler            
   [WEAK]
  308 0000001A                 EXPORT           CAN1_RX1_IRQHandler            
   [WEAK]
  309 0000001A                 EXPORT           CAN1_EWMC_IRQHandler           
   [WEAK]
  310 0000001A                 EXPORT           USBFS_IRQHandler               
   [WEAK]
  311 0000001A                 EXPORT           DMA1_Channel5_IRQHandler       
   [WEAK]
  312 0000001A                 EXPORT           DMA1_Channel6_IRQHandler       
   [WEAK]
  313 0000001A                 EXPORT           DMA1_Channel7_IRQHandler       
   [WEAK]
  314 0000001A                 EXPORT           USART5_IRQHandler              
   [WEAK]
  315 0000001A                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  316 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  317 0000001A                 EXPORT           USBHS_EP1_Out_IRQHandler       
   [WEAK]
  318 0000001A                 EXPORT           USBHS_EP1_In_IRQHandler        
   [WEAK]
  319 0000001A                 EXPORT           USBHS_WKUP_IRQHandler          
   [WEAK]



ARM Macro Assembler    Page 10 


  320 0000001A                 EXPORT           USBHS_IRQHandler               
   [WEAK]
  321 0000001A                 EXPORT           DCI_IRQHandler                 
   [WEAK]
  322 0000001A                 EXPORT           TRNG_IRQHandler                
   [WEAK]
  323 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  324 0000001A                 EXPORT           UART6_IRQHandler               
   [WEAK]
  325 0000001A                 EXPORT           UART7_IRQHandler               
   [WEAK]
  326 0000001A                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  327 0000001A                 EXPORT           SPI4_IRQHandler                
   [WEAK]
  328 0000001A                 EXPORT           SPI5_IRQHandler                
   [WEAK]
  329 0000001A                 EXPORT           TLI_IRQHandler                 
   [WEAK]
  330 0000001A                 EXPORT           TLI_ER_IRQHandler              
   [WEAK]
  331 0000001A                 EXPORT           IPA_IRQHandler                 
   [WEAK]
  332 0000001A         
  333 0000001A         ;/* external interrupts handler */
  334 0000001A         WWDGT_IRQHandler
  335 0000001A         LVD_IRQHandler
  336 0000001A         TAMPER_STAMP_IRQHandler
  337 0000001A         RTC_WKUP_IRQHandler
  338 0000001A         FMC_IRQHandler
  339 0000001A         RCU_CTC_IRQHandler
  340 0000001A         EXTI0_IRQHandler
  341 0000001A         EXTI1_IRQHandler
  342 0000001A         EXTI2_IRQHandler
  343 0000001A         EXTI3_IRQHandler
  344 0000001A         EXTI4_IRQHandler
  345 0000001A         DMA0_Channel0_IRQHandler
  346 0000001A         DMA0_Channel1_IRQHandler
  347 0000001A         DMA0_Channel2_IRQHandler
  348 0000001A         DMA0_Channel3_IRQHandler
  349 0000001A         DMA0_Channel4_IRQHandler
  350 0000001A         DMA0_Channel5_IRQHandler
  351 0000001A         DMA0_Channel6_IRQHandler
  352 0000001A         ADC_IRQHandler
  353 0000001A         CAN0_TX_IRQHandler
  354 0000001A         CAN0_RX0_IRQHandler
  355 0000001A         CAN0_RX1_IRQHandler
  356 0000001A         CAN0_EWMC_IRQHandler
  357 0000001A         EXTI5_9_IRQHandler
  358 0000001A         TIMER0_BRK_TIMER8_IRQHandler
  359 0000001A         TIMER0_UP_TIMER9_IRQHandler
  360 0000001A         TIMER0_TRG_CMT_TIMER10_IRQHandler
  361 0000001A         TIMER0_Channel_IRQHandler
  362 0000001A         TIMER1_IRQHandler
  363 0000001A         TIMER2_IRQHandler
  364 0000001A         TIMER3_IRQHandler
  365 0000001A         I2C0_EV_IRQHandler
  366 0000001A         I2C0_ER_IRQHandler



ARM Macro Assembler    Page 11 


  367 0000001A         I2C1_EV_IRQHandler
  368 0000001A         I2C1_ER_IRQHandler
  369 0000001A         SPI0_IRQHandler
  370 0000001A         SPI1_IRQHandler
  371 0000001A         USART0_IRQHandler
  372 0000001A         USART1_IRQHandler
  373 0000001A         USART2_IRQHandler
  374 0000001A         EXTI10_15_IRQHandler
  375 0000001A         RTC_Alarm_IRQHandler
  376 0000001A         USBFS_WKUP_IRQHandler
  377 0000001A         TIMER7_BRK_TIMER11_IRQHandler
  378 0000001A         TIMER7_UP_TIMER12_IRQHandler
  379 0000001A         TIMER7_TRG_CMT_TIMER13_IRQHandler
  380 0000001A         TIMER7_Channel_IRQHandler
  381 0000001A         DMA0_Channel7_IRQHandler
  382 0000001A         EXMC_IRQHandler
  383 0000001A         SDIO_IRQHandler
  384 0000001A         TIMER4_IRQHandler
  385 0000001A         SPI2_IRQHandler
  386 0000001A         UART3_IRQHandler
  387 0000001A         UART4_IRQHandler
  388 0000001A         TIMER5_DAC_IRQHandler
  389 0000001A         TIMER6_IRQHandler
  390 0000001A         DMA1_Channel0_IRQHandler
  391 0000001A         DMA1_Channel1_IRQHandler
  392 0000001A         DMA1_Channel2_IRQHandler
  393 0000001A         DMA1_Channel3_IRQHandler
  394 0000001A         DMA1_Channel4_IRQHandler
  395 0000001A         ENET_IRQHandler
  396 0000001A         ENET_WKUP_IRQHandler
  397 0000001A         CAN1_TX_IRQHandler
  398 0000001A         CAN1_RX0_IRQHandler
  399 0000001A         CAN1_RX1_IRQHandler
  400 0000001A         CAN1_EWMC_IRQHandler
  401 0000001A         USBFS_IRQHandler
  402 0000001A         DMA1_Channel5_IRQHandler
  403 0000001A         DMA1_Channel6_IRQHandler
  404 0000001A         DMA1_Channel7_IRQHandler
  405 0000001A         USART5_IRQHandler
  406 0000001A         I2C2_EV_IRQHandler
  407 0000001A         I2C2_ER_IRQHandler
  408 0000001A         USBHS_EP1_Out_IRQHandler
  409 0000001A         USBHS_EP1_In_IRQHandler
  410 0000001A         USBHS_WKUP_IRQHandler
  411 0000001A         USBHS_IRQHandler
  412 0000001A         DCI_IRQHandler
  413 0000001A         TRNG_IRQHandler
  414 0000001A         FPU_IRQHandler
  415 0000001A         UART6_IRQHandler
  416 0000001A         UART7_IRQHandler
  417 0000001A         SPI3_IRQHandler
  418 0000001A         SPI4_IRQHandler
  419 0000001A         SPI5_IRQHandler
  420 0000001A         TLI_IRQHandler
  421 0000001A         TLI_ER_IRQHandler
  422 0000001A         IPA_IRQHandler
  423 0000001A         
  424 0000001A E7FE            B                .
  425 0000001C                 ENDP



ARM Macro Assembler    Page 12 


  426 0000001C         
  427 0000001C                 ALIGN
  428 0000001C         
  429 0000001C         ; user Initial Stack & Heap
  430 0000001C         
  431 0000001C                 IF               :DEF:__MICROLIB
  432 0000001C         
  433 0000001C                 EXPORT           __initial_sp
  434 0000001C                 EXPORT           __heap_base
  435 0000001C                 EXPORT           __heap_limit
  436 0000001C         
  437 0000001C                 ELSE
  452                          ENDIF
  453 0000001C         
  454 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\objects\startup_gd32f450_470.d -o.\objects\startup_gd32f45
0_470.o -I.\RTE\_CIMC_GD32_Template -ID:\Keil5\Packs\ARM\CMSIS\5.9.0\CMSIS\Core
\Include -ID:\Keil5\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include --p
redefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 539" --predefi
ne="_RTE_ SETA 1" --predefine="GD32F470 SETA 1" --predefine="_RTE_ SETA 1" --li
st=.\listings\startup_gd32f450_470.lst ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_
gd32f450_470.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 44 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 45 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000800

Symbol: __initial_sp
   Definitions
      At line 46 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      At line 69 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 433 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 55 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 57 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 56 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      At line 434 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
Comment: __heap_base used once
__heap_limit 00000800

Symbol: __heap_limit
   Definitions
      At line 58 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      At line 435 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 64 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 69 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      At line 65 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 181 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

__Vectors_End 000001AC

Symbol: __Vectors_End
   Definitions
      At line 179 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 66 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 181 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 183 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      None
Comment: .text unused
ADC_IRQHandler 0000001A

Symbol: ADC_IRQHandler
   Definitions
      At line 352 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 105 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 261 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 212 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 74 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 213 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN0_EWMC_IRQHandler 0000001A

Symbol: CAN0_EWMC_IRQHandler
   Definitions
      At line 356 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 109 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 265 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN0_RX0_IRQHandler 0000001A

Symbol: CAN0_RX0_IRQHandler
   Definitions
      At line 354 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 107 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 263 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN0_RX1_IRQHandler 0000001A

Symbol: CAN0_RX1_IRQHandler



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 355 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 108 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 264 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN0_TX_IRQHandler 0000001A

Symbol: CAN0_TX_IRQHandler
   Definitions
      At line 353 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 106 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 262 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN1_EWMC_IRQHandler 0000001A

Symbol: CAN1_EWMC_IRQHandler
   Definitions
      At line 400 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 153 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 309 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN1_RX0_IRQHandler 0000001A

Symbol: CAN1_RX0_IRQHandler
   Definitions
      At line 398 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 151 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 307 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 399 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 152 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 308 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

CAN1_TX_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: CAN1_TX_IRQHandler
   Definitions
      At line 397 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 150 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 306 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DCI_IRQHandler 0000001A

Symbol: DCI_IRQHandler
   Definitions
      At line 412 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 165 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 321 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel0_IRQHandler 0000001A

Symbol: DMA0_Channel0_IRQHandler
   Definitions
      At line 345 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 98 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 254 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel1_IRQHandler 0000001A

Symbol: DMA0_Channel1_IRQHandler
   Definitions
      At line 346 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 99 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 255 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel2_IRQHandler 0000001A

Symbol: DMA0_Channel2_IRQHandler
   Definitions
      At line 347 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 100 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 256 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel3_IRQHandler 0000001A

Symbol: DMA0_Channel3_IRQHandler



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 348 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 101 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 257 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel4_IRQHandler 0000001A

Symbol: DMA0_Channel4_IRQHandler
   Definitions
      At line 349 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 102 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 258 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel5_IRQHandler 0000001A

Symbol: DMA0_Channel5_IRQHandler
   Definitions
      At line 350 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 103 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 259 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel6_IRQHandler 0000001A

Symbol: DMA0_Channel6_IRQHandler
   Definitions
      At line 351 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 104 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 260 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA0_Channel7_IRQHandler 0000001A

Symbol: DMA0_Channel7_IRQHandler
   Definitions
      At line 381 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 134 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 290 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel0_IRQHandler 0000001A




ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

Symbol: DMA1_Channel0_IRQHandler
   Definitions
      At line 390 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 143 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 299 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 391 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 144 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 300 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel2_IRQHandler 0000001A

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 392 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 145 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 301 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel3_IRQHandler 0000001A

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 393 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 146 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 302 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 394 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 147 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 303 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel5_IRQHandler 0000001A



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols


Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 402 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 155 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 311 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 403 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 156 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 312 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DMA1_Channel7_IRQHandler 0000001A

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 404 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 157 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 313 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 226 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 81 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 227 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 241 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      None
Comment: Default_Handler unused
ENET_IRQHandler 0000001A

Symbol: ENET_IRQHandler
   Definitions



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

      At line 395 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 148 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 304 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

ENET_WKUP_IRQHandler 0000001A

Symbol: ENET_WKUP_IRQHandler
   Definitions
      At line 396 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 149 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 305 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXMC_IRQHandler 0000001A

Symbol: EXMC_IRQHandler
   Definitions
      At line 382 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 135 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 291 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 340 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 93 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 249 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI10_15_IRQHandler 0000001A

Symbol: EXTI10_15_IRQHandler
   Definitions
      At line 374 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 127 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 283 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols

      At line 341 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 94 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 250 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 342 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 95 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 251 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 343 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 96 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 252 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 344 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 97 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 253 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

EXTI5_9_IRQHandler 0000001A

Symbol: EXTI5_9_IRQHandler
   Definitions
      At line 357 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 110 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 266 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

FMC_IRQHandler 0000001A

Symbol: FMC_IRQHandler
   Definitions
      At line 338 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 91 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 247 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 414 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 168 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 323 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 202 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 72 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 203 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

I2C0_ER_IRQHandler 0000001A

Symbol: I2C0_ER_IRQHandler
   Definitions
      At line 366 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 119 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 275 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

I2C0_EV_IRQHandler 0000001A

Symbol: I2C0_EV_IRQHandler
   Definitions
      At line 365 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 118 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 274 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 368 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 121 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

s
      At line 277 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 367 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 120 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 276 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 407 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 160 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 316 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 406 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 159 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 315 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

IPA_IRQHandler 0000001A

Symbol: IPA_IRQHandler
   Definitions
      At line 422 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 177 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 331 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

LVD_IRQHandler 0000001A

Symbol: LVD_IRQHandler
   Definitions
      At line 335 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 88 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 244 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 207 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 73 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 208 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 197 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 71 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 198 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 231 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 83 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 232 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

RCU_CTC_IRQHandler 0000001A

Symbol: RCU_CTC_IRQHandler
   Definitions
      At line 339 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 92 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 248 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 375 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 128 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 284 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols


RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 337 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 90 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 246 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 186 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 70 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 187 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SDIO_IRQHandler 0000001A

Symbol: SDIO_IRQHandler
   Definitions
      At line 383 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 136 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 292 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SPI0_IRQHandler 0000001A

Symbol: SPI0_IRQHandler
   Definitions
      At line 369 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 122 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 278 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 370 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 123 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 279 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s




ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 385 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 138 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 294 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SPI3_IRQHandler 0000001A

Symbol: SPI3_IRQHandler
   Definitions
      At line 417 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 171 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 326 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SPI4_IRQHandler 0000001A

Symbol: SPI4_IRQHandler
   Definitions
      At line 418 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 172 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 327 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SPI5_IRQHandler 0000001A

Symbol: SPI5_IRQHandler
   Definitions
      At line 419 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 173 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 328 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 221 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 80 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 222 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s




ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 236 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 84 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 237 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TAMPER_STAMP_IRQHandler 0000001A

Symbol: TAMPER_STAMP_IRQHandler
   Definitions
      At line 336 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 89 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 245 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER0_BRK_TIMER8_IRQHandler 0000001A

Symbol: TIMER0_BRK_TIMER8_IRQHandler
   Definitions
      At line 358 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 111 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 267 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER0_Channel_IRQHandler 0000001A

Symbol: TIMER0_Channel_IRQHandler
   Definitions
      At line 361 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 114 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 270 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER0_TRG_CMT_TIMER10_IRQHandler 0000001A

Symbol: TIMER0_TRG_CMT_TIMER10_IRQHandler
   Definitions
      At line 360 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 113 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 269 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER0_UP_TIMER9_IRQHandler 0000001A



ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIMER0_UP_TIMER9_IRQHandler
   Definitions
      At line 359 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 112 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 268 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER1_IRQHandler 0000001A

Symbol: TIMER1_IRQHandler
   Definitions
      At line 362 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 115 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 271 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER2_IRQHandler 0000001A

Symbol: TIMER2_IRQHandler
   Definitions
      At line 363 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 116 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 272 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER3_IRQHandler 0000001A

Symbol: TIMER3_IRQHandler
   Definitions
      At line 364 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 117 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 273 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER4_IRQHandler 0000001A

Symbol: TIMER4_IRQHandler
   Definitions
      At line 384 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 137 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 293 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s




ARM Macro Assembler    Page 16 Alphabetic symbol ordering
Relocatable symbols

TIMER5_DAC_IRQHandler 0000001A

Symbol: TIMER5_DAC_IRQHandler
   Definitions
      At line 388 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 141 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 297 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER6_IRQHandler 0000001A

Symbol: TIMER6_IRQHandler
   Definitions
      At line 389 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 142 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 298 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER7_BRK_TIMER11_IRQHandler 0000001A

Symbol: TIMER7_BRK_TIMER11_IRQHandler
   Definitions
      At line 377 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 130 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 286 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER7_Channel_IRQHandler 0000001A

Symbol: TIMER7_Channel_IRQHandler
   Definitions
      At line 380 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 133 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 289 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TIMER7_TRG_CMT_TIMER13_IRQHandler 0000001A

Symbol: TIMER7_TRG_CMT_TIMER13_IRQHandler
   Definitions
      At line 379 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 132 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 288 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s



ARM Macro Assembler    Page 17 Alphabetic symbol ordering
Relocatable symbols


TIMER7_UP_TIMER12_IRQHandler 0000001A

Symbol: TIMER7_UP_TIMER12_IRQHandler
   Definitions
      At line 378 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 131 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 287 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TLI_ER_IRQHandler 0000001A

Symbol: TLI_ER_IRQHandler
   Definitions
      At line 421 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 176 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 330 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TLI_IRQHandler 0000001A

Symbol: TLI_IRQHandler
   Definitions
      At line 420 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 175 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 329 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

TRNG_IRQHandler 0000001A

Symbol: TRNG_IRQHandler
   Definitions
      At line 413 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 167 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 322 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

UART3_IRQHandler 0000001A

Symbol: UART3_IRQHandler
   Definitions
      At line 386 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 139 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 295 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.



ARM Macro Assembler    Page 18 Alphabetic symbol ordering
Relocatable symbols

s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions
      At line 387 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 140 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 296 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

UART6_IRQHandler 0000001A

Symbol: UART6_IRQHandler
   Definitions
      At line 415 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 169 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 324 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

UART7_IRQHandler 0000001A

Symbol: UART7_IRQHandler
   Definitions
      At line 416 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 170 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 325 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USART0_IRQHandler 0000001A

Symbol: USART0_IRQHandler
   Definitions
      At line 371 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 124 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 280 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 372 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 125 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s



ARM Macro Assembler    Page 19 Alphabetic symbol ordering
Relocatable symbols

      At line 281 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 373 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 126 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 282 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USART5_IRQHandler 0000001A

Symbol: USART5_IRQHandler
   Definitions
      At line 405 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 158 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 314 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USBFS_IRQHandler 0000001A

Symbol: USBFS_IRQHandler
   Definitions
      At line 401 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 154 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 310 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USBFS_WKUP_IRQHandler 0000001A

Symbol: USBFS_WKUP_IRQHandler
   Definitions
      At line 376 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 129 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 285 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USBHS_EP1_In_IRQHandler 0000001A

Symbol: USBHS_EP1_In_IRQHandler
   Definitions
      At line 409 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 162 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.



ARM Macro Assembler    Page 20 Alphabetic symbol ordering
Relocatable symbols

s
      At line 318 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USBHS_EP1_Out_IRQHandler 0000001A

Symbol: USBHS_EP1_Out_IRQHandler
   Definitions
      At line 408 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 161 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 317 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USBHS_IRQHandler 0000001A

Symbol: USBHS_IRQHandler
   Definitions
      At line 411 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 164 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 320 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

USBHS_WKUP_IRQHandler 0000001A

Symbol: USBHS_WKUP_IRQHandler
   Definitions
      At line 410 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 163 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
      At line 319 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 217 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 75 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
      At line 218 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

WWDGT_IRQHandler 0000001A

Symbol: WWDGT_IRQHandler
   Definitions
      At line 334 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 87 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s



ARM Macro Assembler    Page 21 Alphabetic symbol ordering
Relocatable symbols

      At line 243 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s

101 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000800

Symbol: Heap_Size
   Definitions
      At line 53 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      At line 57 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
Comment: Heap_Size used once
Stack_Size 00000800

Symbol: Stack_Size
   Definitions
      At line 42 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
   Uses
      At line 45 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
Comment: Stack_Size used once
__Vectors_Size 000001AC

Symbol: __Vectors_Size
   Definitions
      At line 181 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 67 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 188 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 190 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 189 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
   Uses
      At line 192 in file ..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.
s
Comment: __main used once
2 symbols
453 symbols in table
