#ifndef __CRC_APP_H_
#define __CRC_APP_H_

#include "HeaderFiles.h"

// 原有CRC函数
uint16_t Calculate_CRC16(uint8_t *data, uint16_t length);
void ProcessMessage(uint8_t *message, uint16_t length);

// 新增协议解析函数 - 从任务13开始
void Parse_Protocol_Message(uint8_t* buffer, uint16_t length);
uint8_t Validate_Protocol_Message(uint8_t* buffer, uint16_t length);
void Handle_Get_Device_ID_Protocol(uint8_t* buffer);
void Handle_Set_Device_ID_Protocol(uint8_t* buffer);
void Handle_Single_Sample_Protocol(uint8_t* buffer);
void Handle_Continuous_Sample_Protocol(uint8_t* buffer);
void Handle_Stop_Sample_Protocol(uint8_t* buffer);

// IEEE 754转换函数
uint32_t Float_To_IEEE754(float value);

// IEEE 754输出函数
void Generate_IEEE754_Output(rtc_parameter_struct* rtc_time, float ch0_actual, float ch1_actual, float ch2_actual);

#endif
