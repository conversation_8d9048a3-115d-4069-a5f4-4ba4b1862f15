#ifndef __ADC_APP_H
#define __ADC_APP_H

#include "HeaderFiles.h"

typedef enum {
    ADC_STATE_IDLE = 0,     // 空闲状态
    ADC_STATE_SAMPLING,     // 采样状态
    ADC_STATE_STOPPED       // 停止状态
} adc_state_t;

typedef enum {
    SYSTEM_STATE_IDLE = 0,    // 系统空闲状态
    SYSTEM_STATE_SAMPLING,    // 系统采样状态
    SYSTEM_STATE_CONFIG       // 系统配置状态
} system_state_t;

typedef struct {
    uint16_t raw_value;     // ADC原始值
    float voltage;          // 计算后的电压值
    uint32_t timestamp;     // 时间戳
} adc_data_t;

extern float current_ratio;
extern float ch0_ratio;
extern float ch1_ratio;
extern float ch2_ratio;
extern float ch0_limit;
extern float ch1_limit;
extern float ch2_limit;
extern adc_state_t adc_state;
extern system_state_t system_state;
extern adc_data_t latest_data;
extern uint32_t sample_interval;
extern float ch0_data;
extern float ch1_data;
extern float ch2_data;
extern uint8_t intensity_flag;
extern uint8_t intensity_flag_single;
extern uint8_t res_flag;
extern uint8_t res_flag_single;
extern uint8_t vol_flag;
extern uint8_t vol_flag_single;
extern uint8_t read_mode;       //0-电压读取    1-电流读取    2-电阻读取

void Set_Ratio(float ratio);
float Get_Ratio(void);
void Set_Limit(float limit);
float Get_Limit(void);
void Set_Over_Limit_State(uint8_t state);
void Start_Sampling(void);
void Stop_Sampling(void);
adc_state_t Get_ADC_State(void);
adc_data_t Get_Latest_Data(void);
float ADC_To_Voltage(uint16_t adc_value);
void Set_System_State(system_state_t state);
system_state_t Get_System_State(void);
void Update_LED_Status(void);
void Set_Sample_Interval(uint32_t interval_ms);
uint32_t Get_Sample_Interval(void);
void ADC_Proc(void);

// 高精度校准函数声明
float CH0_Voltage_Calibration(float measured_voltage);
float CH1_Current_Calibration(float measured_current);
float CH2_Resistance_Calibration(float measured_resistance);

#endif
