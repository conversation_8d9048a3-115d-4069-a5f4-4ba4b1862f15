# 六个按键OLED显示功能实现（简化版）

## 功能概述

**实现时间**: 2025-01-11  
**修改文件**: `sysFunction/Key_APP.c`, `sysFunction/Key_APP.h`  
**功能描述**: 通过六个按键控制OLED显示不同通道的原始数据和变比后数据

## 按键功能定义

| 按键 | 功能 | 显示内容 |
|------|------|----------|
| KEY1 | 显示Ch0原始数据 | `Ch0 Raw Data` + `xxxx.xxxx V` |
| KEY2 | 显示Ch1原始数据 | `Ch1 Raw Data` + `xxxx.xxxx A` |
| KEY3 | 显示Ch2原始数据 | `Ch2 Raw Data` + `xxxx.xxxx Ohm` |
| KEY4 | 显示Ch0变比后数据 | `Ch0 Ratio Data` + `xxxx.xxxx V` |
| KEY5 | 显示Ch1变比后数据 | `Ch1 Ratio Data` + `xxxx.xxxx A` |
| KEY6 | 显示Ch2变比后数据 | `Ch2 Ratio Data` + `xxxx.xxxx Ohm` |

## 实现特点

### 1. 简化设计原则
- **直接实现**: 每个按键函数中直接包含OLED显示格式化代码
- **无状态管理**: 不使用复杂的模式枚举和状态机
- **即时显示**: 按键按下后立即更新OLED显示
- **代码清晰**: 每个功能独立，易于理解和维护

### 2. 代码结构
```c
// 典型按键处理函数结构
void Handle_Key1_Press(void)
{
    extern float ch0_data;
    extern char oled1_buffer[128], oled2_buffer[128];
    
    // 直接格式化显示内容
    strcpy(oled1_buffer, "Ch0 Raw Data");
    snprintf(oled2_buffer, sizeof(oled2_buffer), "%.4f V", ch0_data);
    
    // 立即更新OLED显示
    OLED_ShowString(0, 0, (unsigned char *)oled1_buffer, 16);
    OLED_ShowString(0, 16, (unsigned char *)oled2_buffer, 16);
    OLED_Refresh();
    
    // 记录日志
    Write_Log_Data("OLED display: Ch0 raw data (key1 press)");
}
```

## 原有功能保留

所有原有的按键功能都已完整注释保留，包括：
- KEY1: 启动/停止采集功能
- KEY2: 设置5秒采样周期
- KEY3: 设置10秒采样周期  
- KEY4: 设置15秒采样周期

如需恢复原有功能，只需取消注释相应代码块。

## 数据源说明

### 原始数据变量
- `ch0_data`: 通道0原始电压数据 (V)
- `ch1_data`: 通道1原始电流数据 (A)  
- `ch2_data`: 通道2原始电阻数据 (Ohm)

### 变比系数变量
- `ch0_ratio`: 通道0变比系数
- `ch1_ratio`: 通道1变比系数
- `ch2_ratio`: 通道2变比系数

### 计算公式
- 变比后数据 = 原始数据 × 变比系数
- 例如: `ch0_data * ch0_ratio`

## 显示格式

### OLED显示布局
```
行1: [功能描述]     (16字符)
行2: [数值 单位]    (16字符)
```

### 数值精度
- 所有数值显示精度: 4位小数 (`%.4f`)
- 单位标识:
  - 电压: `V`
  - 电流: `A`  
  - 电阻: `Ohm`

## 系统集成

### 初始化显示
- 系统启动: `System Init` + `Loading...`
- 系统就绪: `System Ready` + `Press Key 1-6`
- 停止采样: `Sampling Stop` + `Press Key 1-6`

### 日志记录
每次按键操作都会记录到系统日志：
```
OLED display: Ch0 raw data (key1 press)
OLED display: Ch1 ratio data (key5 press)
```

## 技术优势

1. **简单直观**: 代码逻辑清晰，易于理解
2. **维护性强**: 每个功能独立，修改影响范围小
3. **响应迅速**: 无复杂状态判断，按键响应快
4. **扩展容易**: 添加新显示模式只需增加按键处理函数
5. **调试友好**: 问题定位简单，功能边界清晰

## 使用说明

1. **查看原始数据**: 按下KEY1/KEY2/KEY3查看对应通道原始测量值
2. **查看变比数据**: 按下KEY4/KEY5/KEY6查看对应通道变比后的实际值
3. **实时更新**: 显示的数据会根据当前最新的ADC采样值实时更新
4. **单位显示**: 每个通道都有对应的物理单位标识

---

*此实现采用最简化的设计思路，确保功能可靠、代码清晰、维护简单。*
