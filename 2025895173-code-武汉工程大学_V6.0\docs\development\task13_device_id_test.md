# 任务13：设备ID读取协议实现与测试

## 实现概述

已完成任务13的设备ID读取协议解析功能，支持上位机下发读取设备ID号命令，MCU返回设备ID。

## 协议格式

### 下发命令格式
```
command: FFFF0200080163FA
```

**报文解析：**
- 设备ID（2字节）：`0xFFFF` 代表广播发送
- 系统报文类型（1字节）：`0x02` 代表获取设备ID
- 报文长度（2字节）：`0x0008` 代表报文长度8字节
- 协议版本（1字节）：`0x01` 代表报文协议版本为1
- CRC校验（2字节）：`0x63FA` （按小端序传输）

### 上报响应格式
```
report: 000102000A010001F1C2
```

**报文解析：**
- 设备ID（2字节）：`0x0001` 代表设备ID为0001
- 系统报文类型（1字节）：`0x02` 代表应答
- 报文长度（2字节）：`0x000A` 代表报文长度10字节
- 协议版本（1字节）：`0x01` 代表报文协议版本为1
- 报文内容（2字节）：`0x0001` 此处为获取设备ID，所以报文内容为设备ID
- CRC校验（2字节）：`0xF1C2` （按小端序传输）

## 代码实现

### 1. 协议验证函数
```c
uint8_t Validate_Protocol_Message(uint8_t* buffer, uint16_t length)
```
- 检查最小报文长度（8字节）
- 验证报文长度字段与实际长度匹配
- 检查协议版本为0x01
- 验证CRC-16/MODBUS校验

### 2. 设备ID处理函数
```c
void Handle_Get_Device_ID_Protocol(uint8_t* buffer)
```
- 获取当前设备ID
- 构建标准响应报文
- 计算并添加CRC校验
- 通过RS485发送响应

### 3. 主解析函数
```c
void Parse_Protocol_Message(uint8_t* buffer, uint16_t length)
```
- 提取目标设备ID，支持广播地址(0xFFFF)
- 检查是否为本设备的消息
- 根据系统报文类型分发处理

## 集成方式

### RS485_Task函数修改
在现有的字符串命令解析之前，添加二进制协议检测：

```c
// 首先尝试二进制协议解析 (任务13：设备ID读取)
if(rs485_rx_index >= 8) // 最小协议长度
{
    if(Validate_Protocol_Message(rs485_rx_buffer, rs485_rx_index))
    {
        Parse_Protocol_Message(rs485_rx_buffer, rs485_rx_index);
        goto cleanup;
    }
}
```

## 测试方法

### 1. 发送测试命令
通过串口调试工具发送十六进制数据：
```
FFFF0200080163FA
```

### 2. 预期响应
如果当前设备ID为0x0001，应收到：
```
000102000A010001F1C2
```

### 3. 验证步骤
1. 确认报文长度为10字节
2. 验证设备ID字段为当前设备ID
3. 检查CRC校验是否正确
4. 确认不影响原有字符串命令功能

## 技术特点

### 1. 兼容性设计
- 不影响现有字符串命令解析
- 二进制协议优先检测
- 失败时自动回退到字符串解析

### 2. 错误处理
- 长度检查防止缓冲区溢出
- CRC校验确保数据完整性
- 设备ID过滤避免误响应

### 3. 小端序处理
- 严格按照协议要求使用小端序
- 多字节数据正确拆分和组装

## 后续扩展

当前实现为任务13的基础版本，后续可以在此基础上添加：
- 任务14：设备ID修改功能
- 任务15：单次采集功能
- 任务16：连续采集功能
- 任务17：停止采集功能

每个功能都将复用现有的协议解析框架，只需添加对应的处理函数即可。
