# 字符串格式协议测试指南

## 协议格式说明

### 输入格式
您输入的是带command:前缀的十六进制字符串，例如：
```
command:FFFF0200080163FA
```

这个字符串代表：
- `FFFF` - 设备ID (广播地址)
- `02` - 系统报文类型 (获取设备ID)
- `0008` - 报文长度 (8字节)
- `01` - 协议版本
- `63FA` - CRC校验

### 输出格式
系统会返回十六进制字符串，例如：
```
report:000102000A010001F1C2
```

这个响应代表：
- `0001` - 设备ID (0x0001)
- `02` - 系统报文类型 (应答)
- `000A` - 报文长度 (10字节)
- `01` - 协议版本
- `0001` - 报文内容 (设备ID)
- `F1C2` - CRC校验

## 测试步骤

### 1. 基本测试
输入：
```
command:FFFF0200080163FA
```

预期输出：
```
report:000102000A010001F1C2
```

### 2. 调试功能
如果没有响应，可以输入：
```
debug
```

系统会显示接收到的数据，帮助诊断问题。

### 3. 验证现有功能
确保原有字符串命令仍然工作：
```
command:get_device_id
```

应该返回：
```
report:device_id=0x0001
```

## 代码实现要点

### 1. 字符串检测
```c
// 检查是否为command:前缀的十六进制协议 (如: command:FFFF0200080163FA)
if(rs485_rx_index >= 24 && strncmp((char*)rs485_rx_buffer, "command:", 8) == 0)
{
    // 检查是否全为十六进制字符
    uint8_t is_hex_string = 1;
    for(uint8_t i = 0; i < rs485_rx_index; i++)
    {
        char c = rs485_rx_buffer[i];
        if(!((c >= '0' && c <= '9') || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f')))
        {
            is_hex_string = 0;
            break;
        }
    }
```

### 2. 字符串转二进制
```c
if(is_hex_string)
{
    // 转换十六进制字符串为二进制数据
    uint8_t binary_data[8];
    
    for(uint8_t i = 0; i < 8; i++)
    {
        char hex_byte[3] = {rs485_rx_buffer[i*2], rs485_rx_buffer[i*2+1], '\0'};
        binary_data[i] = (uint8_t)strtol(hex_byte, NULL, 16);
    }
```

### 3. 字符串格式响应
```c
// 发送响应 - 以十六进制字符串格式输出
extern int rs485_printf(const char *format, ...);
rs485_printf("report:");
for(uint8_t i = 0; i < 10; i++)
{
    rs485_printf("%02X", response[i]);
}
rs485_printf("\r\n");
```

## 可能的问题

### 1. 没有响应
- 检查输入的字符串长度是否为16个字符
- 确认所有字符都是有效的十六进制字符 (0-9, A-F)
- 使用 `debug` 命令查看实际接收到的数据

### 2. CRC校验失败
- 确认输入的CRC是否正确
- 检查字节序是否正确 (小端序)

### 3. 设备ID不匹配
- 使用广播地址 `FFFF` 应该总是有响应
- 检查当前设备ID设置

## 下一步测试

成功实现任务13后，可以测试：
1. 任务14：设备ID修改
2. 任务15：单次采集
3. 任务16：连续采集
4. 任务17：停止采集

每个功能都将使用相同的字符串格式进行输入输出。
