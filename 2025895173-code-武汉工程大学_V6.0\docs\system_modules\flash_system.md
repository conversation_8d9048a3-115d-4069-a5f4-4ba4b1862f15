# Flash系统详细文档

## 模块概述

Flash系统是项目的核心非易失性存储模块，采用外部SPI Flash芯片实现数据的持久化存储。该模块分为两层架构：底层SPI Flash驱动层和上层Flash应用层，提供设备ID存储、配置参数管理、日志缓存等功能。

### 主要功能
- **设备ID管理**: 存储和读取设备唯一标识符
- **配置参数持久化**: 保存系统配置参数(变比、阈值、采样间隔)
- **日志缓存系统**: 临时存储系统日志，作为SD卡的备份方案
- **数据完整性保证**: 通过魔数和校验和确保数据可靠性

### 技术规格
- **Flash芯片**: 支持标准SPI接口Flash (检测ID: 0xC84013)
- **通信接口**: SPI1 (PB12-CS, PB13-CLK, PB14-MISO, PB15-MOSI)
- **页大小**: 256字节
- **扇区大小**: 4096字节 (4KB)
- **通信速度**: 系统时钟/8 (约30MHz)

## 核心数据结构

### 配置存储结构
```c
typedef struct {
    uint32_t magic;           // 魔数标识 (0x43464749)
    float ratio_value;        // 变比值 (0.0-100.0)
    float limit_value;        // 阈值 (0.0-200.0)
    uint32_t sample_interval; // 采样间隔 (1000-15000ms)
    uint32_t checksum;        // 校验和
} config_storage_t;
```

### 重要宏定义
```c
#define SFLASH_ID              0xC84013    // Flash芯片ID
#define FLASH_DEVICE_ID_ADDR   0x000000    // 设备ID存储地址
#define CONFIG_STORAGE_ADDR    0x001000    // 配置存储地址
#define LOG_CACHE_ADDR         0x003000    // 日志缓存地址
#define CONFIG_MAGIC_NUMBER    0x43464749  // 配置区魔数
#define LOG_CACHE_MAGIC        0x4C4F4743  // 日志缓存魔数
#define LOG_CACHE_MAX_SIZE     512         // 日志缓存最大大小
#define DEVICE_ID_MAX_LENGTH   30          // 设备ID最大长度
```

### 存储区域规划
| 区域 | 起始地址 | 大小 | 用途 | 魔数 |
|------|----------|------|------|------|
| 设备ID区 | 0x000000 | 30字节 | 存储设备唯一标识 | - |
| 配置区 | 0x001000 | 20字节 | 系统配置参数 | 0x43464749 |
| 日志缓存区 | 0x003000 | 512字节 | 临时日志缓存 | 0x4C4F4743 |

### 全局变量
```c
extern uint32_t flash_id;        // Flash芯片ID
extern uint32_t current_log_id;  // 当前日志ID
```

## 函数详细说明

### 1. SPI Flash驱动层函数

#### spi_flash_init()
```c
void spi_flash_init(void);
```
**功能**: 初始化SPI Flash硬件接口
**参数**: 无
**返回值**: 无
**作用**: 
- 配置SPI1外设时钟
- 设置GPIO复用功能(PB12-CS, PB13-CLK, PB14-MISO, PB15-MOSI)
- 初始化SPI参数(主模式、8位数据、时钟极性等)
- 使能SPI1外设
**使用场景**: 系统初始化时调用，建立与Flash芯片的通信

#### spi_flash_read_id()
```c
uint32_t spi_flash_read_id(void);
```
**功能**: 读取Flash芯片的设备ID
**参数**: 无
**返回值**: 24位Flash设备ID (期望值: 0xC84013)
**作用**: 
- 发送RDID命令(0x9F)
- 读取3字节设备标识
- 用于验证Flash芯片是否正确连接
**使用场景**: 系统初始化时验证硬件连接

#### spi_flash_buffer_read()
```c
void spi_flash_buffer_read(uint8_t* pbuffer, uint32_t read_addr, uint16_t num_byte_to_read);
```
**功能**: 从Flash指定地址读取数据块
**参数**: 
- `pbuffer`: 接收数据的缓冲区指针
- `read_addr`: Flash中的读取起始地址
- `num_byte_to_read`: 要读取的字节数
**返回值**: 无
**作用**: 
- 发送READ命令(0x03)和24位地址
- 连续读取指定数量的字节
- 自动处理地址边界
**使用场景**: 读取存储的配置、设备ID或日志数据

#### spi_flash_buffer_write()
```c
void spi_flash_buffer_write(uint8_t* pbuffer, uint32_t write_addr, uint32_t num_byte_to_write);
```
**功能**: 向Flash指定地址写入数据块
**参数**: 
- `pbuffer`: 要写入的数据缓冲区指针
- `write_addr`: Flash中的写入起始地址
- `num_byte_to_write`: 要写入的字节数
**返回值**: 无
**作用**: 
- 自动处理页边界对齐
- 分页写入大块数据
- 每页写入后等待操作完成
**使用场景**: 保存配置参数、设备ID或日志数据
**注意**: 写入前必须先擦除对应区域

#### spi_flash_sector_erase()
```c
void spi_flash_sector_erase(uint32_t sector_addr);
```
**功能**: 擦除指定扇区(4KB)
**参数**: 
- `sector_addr`: 要擦除的扇区地址(任意扇区内地址)
**返回值**: 无
**作用**: 
- 发送写使能命令
- 发送扇区擦除命令(0x20)
- 等待擦除操作完成
**使用场景**: 写入数据前的必要准备步骤

#### spi_flash_bulk_erase()
```c
void spi_flash_bulk_erase(void);
```
**功能**: 擦除整个Flash芯片
**参数**: 无
**返回值**: 无
**作用**: 
- 发送写使能命令
- 发送整片擦除命令(0xC7)
- 等待擦除操作完成(耗时较长)
**使用场景**: 系统重置或Flash初始化

#### spi_flash_wait_for_write_end()
```c
void spi_flash_wait_for_write_end(void);
```
**功能**: 等待Flash写操作完成
**参数**: 无
**返回值**: 无
**作用**: 
- 循环读取状态寄存器
- 检查WIP(Write In Progress)标志位
- 确保操作完成后再继续
**使用场景**: 每次写入或擦除操作后调用

#### spi_flash_write_enable()
```c
void spi_flash_write_enable(void);
```
**功能**: 使能Flash写操作
**参数**: 无
**返回值**: 无
**作用**: 
- 发送写使能命令(0x06)
- 设置状态寄存器的WEL位
**使用场景**: 每次写入或擦除操作前调用

### 2. Flash应用层函数

#### Flash_Init()
```c
void Flash_Init(void);
```
**功能**: Flash系统完整初始化
**参数**: 无
**返回值**: 无
**作用**: 
- 初始化SPI Flash驱动
- 读取并验证Flash ID
- 检查并初始化设备ID(首次使用时写入默认ID)
- 读取配置参数并应用到系统
- 初始化日志系统
**使用场景**: 系统启动时调用，完成Flash系统的全面初始化

#### Save_Config_To_Flash()
```c
uint8_t Save_Config_To_Flash(float ratio, float limit, uint32_t interval);
```
**功能**: 保存系统配置参数到Flash
**参数**: 
- `ratio`: 变比值 (0.0-100.0)
- `limit`: 阈值 (0.0-200.0)
- `interval`: 采样间隔 (1000-15000ms)
**返回值**: 
- `1`: 保存成功
- `0`: 保存失败
**作用**: 
- 验证参数范围
- 构建配置结构体
- 计算校验和
- 擦除配置区并写入新配置
**使用场景**: 用户修改系统配置时调用

#### Read_Config_From_Flash()
```c
uint8_t Read_Config_From_Flash(float* ratio, float* limit, uint32_t* interval);
```
**功能**: 从Flash读取系统配置参数
**参数**: 
- `ratio`: 返回变比值的指针
- `limit`: 返回阈值的指针
- `interval`: 返回采样间隔的指针
**返回值**: 
- `1`: 读取成功
- `0`: 读取失败或数据无效
**作用**: 
- 从Flash读取配置数据
- 验证魔数和校验和
- 检查参数范围有效性
- 将有效配置返回给调用者
**使用场景**: 系统启动时恢复配置参数

#### Verify_Config_Data()
```c
uint8_t Verify_Config_Data(config_storage_t* data);
```
**功能**: 验证配置数据的完整性和有效性
**参数**: 
- `data`: 配置数据结构指针
**返回值**: 
- `1`: 数据有效
- `0`: 数据无效
**作用**: 
- 检查魔数是否正确
- 验证校验和
- 检查参数范围
**使用场景**: 读取配置时验证数据可靠性

#### Calculate_Config_Checksum()
```c
uint32_t Calculate_Config_Checksum(config_storage_t* data);
```
**功能**: 计算配置数据的校验和
**参数**: 
- `data`: 配置数据结构指针
**返回值**: 计算得到的校验和值
**作用**: 
- 对配置结构体的前16字节计算校验和
- 使用简单累加算法
**使用场景**: 保存配置时生成校验和，读取时验证数据完整性

### 3. 日志缓存系统函数

#### Log_Init()
```c
uint8_t Log_Init(void);
```
**功能**: 初始化日志系统
**参数**: 无
**返回值**: 
- `1`: 初始化成功
- `0`: 初始化失败
**作用**: 
- 设置当前日志ID
- 初始化日志缓存区
**使用场景**: Flash系统初始化时调用

#### Cache_Log_To_Flash()
```c
uint8_t Cache_Log_To_Flash(const char* log_msg);
```
**功能**: 将日志消息缓存到Flash
**参数**: 
- `log_msg`: 要缓存的日志消息字符串
**返回值**: 
- `1`: 缓存成功
- `0`: 缓存失败
**作用**: 
- 检查消息长度
- 读取现有缓存内容
- 追加新消息到缓存
- 更新缓存长度和魔数
- 写入Flash缓存区
**使用场景**: SD卡不可用时临时存储日志

#### Check_Cached_Log_In_Flash()
```c
uint8_t Check_Cached_Log_In_Flash(void);
```
**功能**: 检查Flash中是否有缓存的日志
**参数**: 无
**返回值**: 
- `1`: 有缓存日志
- `0`: 无缓存日志
**作用**: 
- 读取缓存区魔数
- 验证缓存数据有效性
**使用场景**: SD卡可用时检查是否需要写入缓存日志

#### Write_Cached_Log_To_Log0()
```c
uint8_t Write_Cached_Log_To_Log0(void);
```
**功能**: 将Flash缓存的日志写入SD卡log0.txt文件
**参数**: 无
**返回值**: 
- `1`: 写入成功
- `0`: 写入失败
**作用**: 
- 检查缓存有效性
- 读取缓存内容
- 挂载文件系统
- 创建或打开log0.txt文件
- 写入缓存日志
- 清除Flash缓存
**使用场景**: SD卡首次插入时恢复缓存日志

## 使用示例

### 基本初始化流程
```c
// 系统启动时的Flash初始化
void system_init(void) {
    // 完整的Flash系统初始化
    Flash_Init();  // 自动完成所有初始化工作
    
    // 检查Flash ID
    if(flash_id == SFLASH_ID) {
        printf("Flash初始化成功, ID: 0x%06X\r\n", flash_id);
    } else {
        printf("Flash初始化失败\r\n");
    }
}
```

### 配置参数管理
```c
// 保存系统配置
void save_system_config(void) {
    float ratio = 1.5f;      // 变比1.5
    float limit = 50.0f;     // 阈值50V
    uint32_t interval = 10000; // 采样间隔10秒
    
    if(Save_Config_To_Flash(ratio, limit, interval)) {
        printf("配置保存成功\r\n");
    } else {
        printf("配置保存失败\r\n");
    }
}

// 读取系统配置
void load_system_config(void) {
    float ratio, limit;
    uint32_t interval;
    
    if(Read_Config_From_Flash(&ratio, &limit, &interval)) {
        printf("配置读取成功: 变比=%.2f, 阈值=%.2f, 间隔=%dms\r\n", 
               ratio, limit, interval);
        
        // 应用配置到系统
        Set_Ratio(ratio);
        Set_Limit(limit);
        Set_Sample_Interval(interval);
    } else {
        printf("配置读取失败，使用默认配置\r\n");
    }
}
```

### 日志缓存管理
```c
// 记录日志(自动选择存储位置)
void log_system_event(const char* event) {
    // 优先尝试写入SD卡
    if(Write_Log_Data(event) != 0) {
        // SD卡不可用，缓存到Flash
        Cache_Log_To_Flash(event);
        printf("日志已缓存到Flash\r\n");
    }
}

// SD卡插入后恢复缓存日志
void recover_cached_logs(void) {
    if(Check_Cached_Log_In_Flash()) {
        printf("发现Flash缓存日志，正在恢复...\r\n");
        if(Write_Cached_Log_To_Log0()) {
            printf("缓存日志恢复成功\r\n");
        } else {
            printf("缓存日志恢复失败\r\n");
        }
    }
}
```

### 底层Flash操作
```c
// 直接Flash读写操作示例
void flash_raw_operations(void) {
    uint8_t write_data[] = "Test Data";
    uint8_t read_buffer[20];
    uint32_t test_addr = 0x010000;  // 测试地址
    
    // 擦除扇区
    spi_flash_sector_erase(test_addr);
    
    // 写入数据
    spi_flash_buffer_write(write_data, test_addr, strlen(write_data));
    
    // 读取数据
    spi_flash_buffer_read(read_buffer, test_addr, strlen(write_data));
    
    // 验证数据
    if(memcmp(write_data, read_buffer, strlen(write_data)) == 0) {
        printf("Flash读写测试通过\r\n");
    } else {
        printf("Flash读写测试失败\r\n");
    }
}
```

## 注意事项

### 1. 写操作限制
- **写前必须擦除**: Flash只能将1写成0，不能将0写成1
- **页对齐写入**: 单次写入不能跨越256字节页边界
- **扇区擦除**: 最小擦除单位为4KB扇区
- **写使能**: 每次写入或擦除前必须发送写使能命令

### 2. 数据完整性
- **魔数验证**: 使用魔数标识有效数据区域
- **校验和保护**: 重要数据使用校验和验证完整性
- **参数范围检查**: 配置参数必须在有效范围内
- **重试机制**: 关键操作失败时自动重试

### 3. 性能优化
- **批量操作**: 尽量使用批量读写减少命令开销
- **缓存机制**: 使用RAM缓存减少Flash访问次数
- **异步操作**: 利用等待时间处理其他任务
- **地址对齐**: 按页和扇区边界对齐提高效率

### 4. 错误处理
- **ID验证失败**: Flash硬件连接问题或芯片损坏
- **写入失败**: 可能是写保护或硬件故障
- **数据损坏**: 使用备份和校验机制恢复
- **容量不足**: 合理规划存储区域避免冲突

## 二次开发指南

### 1. 添加新的存储区域
```c
// 1. 定义新区域地址和魔数
#define NEW_DATA_ADDR    0x005000
#define NEW_DATA_MAGIC   0x4E455744  // "NEWD"

// 2. 定义数据结构
typedef struct {
    uint32_t magic;
    // 添加具体数据字段
    uint32_t checksum;
} new_data_t;

// 3. 实现读写函数
uint8_t Save_NewData_To_Flash(/* 参数 */);
uint8_t Read_NewData_From_Flash(/* 参数 */);
```

### 2. 扩展配置参数
```c
// 修改config_storage_t结构
typedef struct {
    uint32_t magic;
    float ratio_value;
    float limit_value;
    uint32_t sample_interval;
    // 添加新参数
    float new_param1;
    uint8_t new_param2;
    uint32_t checksum;
} config_storage_t;

// 更新校验和计算
uint32_t Calculate_Config_Checksum(config_storage_t* data) {
    // 调整计算范围包含新参数
}
```

### 3. 实现数据压缩
```c
// 添加压缩存储功能
uint8_t Save_Compressed_Data(uint8_t* data, uint32_t size, uint32_t addr) {
    // 1. 压缩数据
    // 2. 添加压缩标识
    // 3. 存储到Flash
}

uint8_t Read_Compressed_Data(uint8_t* buffer, uint32_t addr) {
    // 1. 读取数据
    // 2. 检查压缩标识
    // 3. 解压数据
}
```

### 4. 添加数据加密
```c
// 实现加密存储
uint8_t Save_Encrypted_Config(config_storage_t* config) {
    // 1. 加密配置数据
    // 2. 存储到Flash
}

uint8_t Read_Encrypted_Config(config_storage_t* config) {
    // 1. 读取加密数据
    // 2. 解密配置
    // 3. 验证完整性
}
```

### 5. 实现磨损均衡
```c
// 简单的磨损均衡实现
typedef struct {
    uint32_t write_count;
    uint32_t current_sector;
} wear_leveling_t;

uint8_t Write_With_Wear_Leveling(uint8_t* data, uint32_t size) {
    // 1. 选择写入次数最少的扇区
    // 2. 写入数据
    // 3. 更新写入计数
}
```

通过以上详细的函数说明和使用示例，开发者可以全面理解Flash系统的工作原理，并基于现有代码进行功能扩展和性能优化。
