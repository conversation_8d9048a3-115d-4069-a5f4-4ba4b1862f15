# SD卡系统详细文档

## 模块概述

SD卡系统是整个嵌入式项目的核心数据存储模块，负责管理SD卡的文件操作、数据记录和文件系统管理。该模块基于FAT32文件系统，支持多种类型的数据存储，包括采样数据、超限数据、加密数据和系统日志。

### 主要功能
- **多类型数据存储**: 支持采样数据、超限数据、加密数据、日志数据的分类存储
- **文件系统管理**: 基于FAT32文件系统的完整文件操作
- **自动目录创建**: 自动创建和管理不同类型数据的存储目录
- **数据备份机制**: 支持Flash缓存和SD卡双重存储保障
- **文件版本管理**: 自动文件命名和版本控制

## 核心数据结构

### 记录状态枚举
```c
typedef enum 
{
    RECORD_STATE_IDLE = 0,    // 空闲状态
    RECORD_STATE_ACTIVE,      // 记录中
    RECORD_STATE_ERROR        // 错误状态
} record_state_t;
```

### 重要宏定义
```c
#define SAMPLE_DIR_NAME     "sample"      // 采样数据目录名
#define OVERLIMIT_DIR_NAME  "overlimit"   // 超限数据目录名
#define HIDEDATA_DIR_NAME   "hideData"    // 加密数据目录名
#define LOG_DIR_NAME        "log"         // 日志数据目录名
#define DATA_BUFFER_SIZE    256           // 数据缓冲区大小
#define MAX_FILENAME_LEN    64            // 最大文件名长度
#define MAX_RECORDS_PER_FILE 10           // 每个文件最大记录数
```

### 全局变量
```c
extern record_state_t record_state;  // 当前记录状态
extern FIL overlimit_file;          // 超限数据文件对象
extern FIL hidedata_file;           // 加密数据文件对象
extern FIL log_file;                // 日志文件对象
extern FATFS fatfs;                 // 文件系统对象
```

## 函数详细说明

### 1. 系统初始化函数

#### nvic_config()
```c
void nvic_config(void);
```
**功能**: 配置NVIC中断控制器，设置SDIO中断优先级
**参数**: 无
**返回值**: 无
**作用**: 
- 设置中断优先级分组为NVIC_PRIGROUP_PRE1_SUB3
- 使能SDIO中断，优先级设置为0
**使用场景**: 系统初始化时调用，确保SD卡操作的中断正常工作

#### Check_TF_Card()
```c
uint8_t Check_TF_Card(void);
```
**功能**: 检测TF卡(SD卡)是否正确插入
**参数**: 无
**返回值**: 
- `1`: TF卡已插入且可用
- `0`: TF卡未插入或不可用
**作用**: 
- 尝试3次检测SD卡状态
- 检测SD卡的物理连接和电气连接
**使用场景**: 在进行任何SD卡操作前调用，确保硬件连接正常

#### Init_Data_Recording()
```c
uint8_t Init_Data_Recording(void);
```
**功能**: 初始化数据记录系统，创建必要的目录结构
**参数**: 无
**返回值**: 
- `0`: 初始化成功
- `非0`: 初始化失败
**作用**: 
- 挂载FAT32文件系统
- 自动创建sample、overlimit、hideData、log四个目录
- 检查是否首次插入SD卡，如果是则创建log0.txt并写入Flash缓存的日志
- 设置记录状态为RECORD_STATE_ACTIVE
**使用场景**: 系统启动时调用，建立完整的数据存储环境

### 2. 采样数据管理函数

#### Create_Sample_File()
```c
uint8_t Create_Sample_File(void);
```
**功能**: 创建新的采样数据文件
**参数**: 无
**返回值**: 
- `0`: 创建成功
- `非0`: 创建失败
**作用**: 
- 根据当前时间生成唯一的文件名
- 在sample目录下创建新的数据文件
- 重置文件记录计数器
**使用场景**: 开始新的数据采集任务时调用

#### Write_Sample_Data()
```c
uint8_t Write_Sample_Data(const char* timestamp, float voltage, float ratio);
```
**功能**: 写入采样数据到当前文件
**参数**: 
- `timestamp`: 时间戳字符串
- `voltage`: 电压值
- `ratio`: 变比值
**返回值**: 
- `0`: 写入成功
- `非0`: 写入失败
**作用**: 
- 检查记录状态是否为ACTIVE
- 自动管理文件切换(每10条记录创建新文件)
- 格式化数据并写入文件
- 自动同步文件系统
**使用场景**: 每次采集到新数据时调用

#### Close_Sample_File()
```c
uint8_t Close_Sample_File(void);
```
**功能**: 关闭当前的采样数据文件
**参数**: 无
**返回值**: 
- `0`: 关闭成功
- `非0`: 关闭失败
**作用**: 
- 安全关闭文件句柄
- 确保数据完全写入SD卡
**使用场景**: 停止数据采集或系统关闭时调用

### 3. 超限数据管理函数

#### Write_OverLimit_Data()
```c
uint8_t Write_OverLimit_Data(const char* timestamp, float voltage, float limit);
```
**功能**: 写入超限数据到专用文件
**参数**: 
- `timestamp`: 时间戳字符串
- `voltage`: 超限的电压值
- `limit`: 设定的限制值
**返回值**: 
- `0`: 写入成功
- `非0`: 写入失败
**作用**: 
- 自动创建超限数据文件(如果不存在)
- 格式化超限数据并写入
- 管理文件记录计数
**使用场景**: 检测到数据超过设定阈值时调用

#### Close_OverLimit_File()
```c
uint8_t Close_OverLimit_File(void);
```
**功能**: 关闭超限数据文件
**参数**: 无
**返回值**: 
- `0`: 关闭成功
- `非0`: 关闭失败
**作用**: 安全关闭超限数据文件句柄
**使用场景**: 系统关闭或切换模式时调用

### 4. 加密数据管理函数

#### Create_HideData_File()
```c
uint8_t Create_HideData_File(void);
```
**功能**: 创建加密数据文件
**参数**: 无
**返回值**: 
- `0`: 创建成功
- `非0`: 创建失败
**作用**: 在hideData目录下创建新的加密数据文件
**使用场景**: 需要存储敏感数据时调用

#### Write_HideData()
```c
uint8_t Write_HideData(const char* timestamp, float voltage, const char* hex_data);
```
**功能**: 写入加密数据
**参数**: 
- `timestamp`: 时间戳字符串
- `voltage`: 电压值
- `hex_data`: 十六进制格式的加密数据
**返回值**: 
- `0`: 写入成功
- `非0`: 写入失败
**作用**: 
- 格式化加密数据并写入文件
- 管理加密文件的记录计数
**使用场景**: 存储需要加密保护的敏感数据时调用

#### Close_HideData_File()
```c
uint8_t Close_HideData_File(void);
```
**功能**: 关闭加密数据文件
**参数**: 无
**返回值**: 
- `0`: 关闭成功
- `非0`: 关闭失败
**作用**: 安全关闭加密数据文件句柄
**使用场景**: 完成加密数据存储或系统关闭时调用

### 5. 日志管理函数

#### Create_Log_File()
```c
uint8_t Create_Log_File(void);
```
**功能**: 创建系统日志文件
**参数**: 无
**返回值**: 
- `0`: 创建成功
- `非0`: 创建失败
**作用**: 
- 获取下一个可用的日志ID
- 在log目录下创建新的日志文件
**使用场景**: 系统启动或需要记录日志时调用

#### Write_Log_Data()
```c
uint8_t Write_Log_Data(const char* log_msg);
```
**功能**: 写入日志数据
**参数**: 
- `log_msg`: 日志消息字符串
**返回值**: 
- `0`: 写入成功
- `非0`: 写入失败
**作用**: 
- 自动添加时间戳到日志消息
- 如果SD卡不可用，自动缓存到Flash
- 管理日志文件的创建和写入
**使用场景**: 记录系统事件、错误信息、操作日志时调用

#### Get_Next_Log_ID_From_SD()
```c
uint32_t Get_Next_Log_ID_From_SD(void);
```
**功能**: 从SD卡获取下一个可用的日志ID
**参数**: 无
**返回值**: 下一个可用的日志文件ID号
**作用**: 
- 扫描log目录中现有的日志文件
- 计算下一个可用的ID号
- 确保日志文件名的唯一性
**使用场景**: 创建新日志文件时调用

#### Create_Log0_File()
```c
uint8_t Create_Log0_File(void);
```
**功能**: 创建初始日志文件log0.txt
**参数**: 无
**返回值**: 
- `0`: 创建成功
- `非0`: 创建失败
**作用**: 
- 创建系统的第一个日志文件
- 通常在首次插入SD卡时调用
**使用场景**: 系统首次初始化或SD卡首次使用时调用

### 6. 配置文件管理函数

#### Create_Default_Config_INI()
```c
uint8_t Create_Default_Config_INI(void);
```
**功能**: 创建默认的配置文件config.ini
**参数**: 无
**返回值**: 
- `0`: 创建成功
- `非0`: 创建失败
**作用**: 
- 在SD卡根目录创建配置文件
- 写入默认的系统配置参数
**使用场景**: 系统初始化或配置文件丢失时调用

#### Update_Config_INI()
```c
uint8_t Update_Config_INI(float ch0_ratio, float ch1_ratio, float ch2_ratio,
                         float ch0_limit, float ch1_limit, float ch2_limit);
```
**功能**: 更新配置文件中的所有参数
**参数**: 
- `ch0_ratio`: 通道0变比值
- `ch1_ratio`: 通道1变比值
- `ch2_ratio`: 通道2变比值
- `ch0_limit`: 通道0限制值
- `ch1_limit`: 通道1限制值
- `ch2_limit`: 通道2限制值
**返回值**: 
- `0`: 更新成功
- `非0`: 更新失败
**作用**: 
- 更新配置文件中的所有通道参数
- 确保配置的持久化存储
**使用场景**: 用户修改系统配置参数时调用

#### Update_Config_INI_Ratio_Only()
```c
uint8_t Update_Config_INI_Ratio_Only(float ch0_ratio, float ch1_ratio, float ch2_ratio);
```
**功能**: 仅更新配置文件中的变比参数
**参数**: 
- `ch0_ratio`: 通道0变比值
- `ch1_ratio`: 通道1变比值
- `ch2_ratio`: 通道2变比值
**返回值**: 
- `0`: 更新成功
- `非0`: 更新失败
**作用**: 
- 仅更新变比参数，保持其他配置不变
- 提供更精确的配置管理
**使用场景**: 仅需要修改变比参数时调用

### 7. 强健写入函数

#### Write_To_Path_Robust()
```c
void Write_To_Path_Robust(const char* file_path, const char* content);
```
**功能**: 强健的文件写入函数，确保100%成功写入
**参数**: 
- `file_path`: 完整文件路径
- `content`: 要写入的内容
**返回值**: 无(确保成功，无需检查返回值)
**作用**: 
- 多重写入策略确保数据不丢失
- 自动重试机制
- 备用路径写入
- 异常情况下的容错处理
**使用场景**: 需要确保数据绝对不丢失的关键写入操作

#### Append_To_Path_Robust()
```c
void Append_To_Path_Robust(const char* file_path, const char* content);
```
**功能**: 强健的文件追加函数，在文件末尾追加内容
**参数**: 
- `file_path`: 完整文件路径
- `content`: 要追加的内容
**返回值**: 无(确保成功，无需检查返回值)
**作用**: 
- 在现有文件末尾追加内容
- 如果文件不存在则创建新文件
- 多重策略确保追加成功
**使用场景**: 需要在现有文件中追加新数据时调用

## 使用示例

### 基本初始化流程
```c
// 1. 配置中断
nvic_config();

// 2. 检查SD卡
if(Check_TF_Card() == 1) {
    // 3. 初始化数据记录系统
    if(Init_Data_Recording() == 0) {
        // 4. 创建默认配置
        Create_Default_Config_INI();
        // 5. 记录系统启动日志
        Write_Log_Data("system init");
    }
}
```

### 数据采集流程
```c
// 开始采集
if(record_state == RECORD_STATE_ACTIVE) {
    // 写入采样数据
    char timestamp[32];
    sprintf(timestamp, "2025-01-11 14:30:25");
    Write_Sample_Data(timestamp, 3.14f, 1.0f);
    
    // 如果数据超限
    if(voltage > limit) {
        Write_OverLimit_Data(timestamp, voltage, limit);
    }
}

// 停止采集
Close_Sample_File();
Close_OverLimit_File();
```

### 配置管理流程
```c
// 更新所有配置参数
Update_Config_INI(1.0f, 1.0f, 1.0f, 100.0f, 100.0f, 100.0f);

// 仅更新变比参数
Update_Config_INI_Ratio_Only(2.0f, 2.0f, 2.0f);
```

### 日志记录流程
```c
// 记录系统事件
Write_Log_Data("system started");
Write_Log_Data("data collection started");
Write_Log_Data("error: sensor disconnected");
```

## 注意事项

### 1. 文件系统安全
- 所有文件操作后都会调用`f_sync()`确保数据写入
- 使用多重重试机制处理SD卡不稳定的情况
- 提供Flash缓存机制作为备用存储

### 2. 内存管理
- 使用固定大小的缓冲区避免内存碎片
- 及时关闭文件句柄释放资源
- 避免长时间占用文件系统资源

### 3. 错误处理
- 所有函数都有完善的错误检查和处理
- 提供多种备用方案确保数据不丢失
- 异常情况下自动切换到Flash存储

### 4. 性能优化
- 批量写入减少文件系统开销
- 合理的文件大小控制(每文件10条记录)
- 异步写入机制减少阻塞时间

## 二次开发指南

### 1. 添加新的数据类型
```c
// 1. 在头文件中添加新的目录定义
#define NEWDATA_DIR_NAME "newdata"

// 2. 添加对应的文件对象
FIL newdata_file;

// 3. 在Init_Data_Recording()中添加目录创建代码
res = f_mkdir("0:/newdata");

// 4. 实现对应的写入函数
uint8_t Write_NewData(const char* timestamp, float value) {
    // 参考Write_Sample_Data()的实现
}
```

### 2. 修改文件命名规则
```c
// 在Generate_Filename()函数中修改命名格式
snprintf(filename, max_len, "custom_%02x%02x%02x_%02x%02x%02x.txt",
         rtc_time.year, rtc_time.month, rtc_time.date,
         rtc_time.hour, rtc_time.minute, rtc_time.second);
```

### 3. 扩展配置文件功能
```c
// 添加新的配置参数更新函数
uint8_t Update_Config_INI_Custom(float new_param1, int new_param2) {
    // 参考现有的Update_Config_INI()实现
    // 添加新参数的格式化和写入逻辑
}
```

### 4. 实现数据压缩
```c
// 在写入函数中添加压缩逻辑
uint8_t Write_Compressed_Data(const char* data) {
    // 1. 压缩数据
    // 2. 调用现有的写入函数
    // 3. 添加压缩标识
}
```

### 5. 添加数据加密
```c
// 扩展Write_HideData()函数
uint8_t Write_Encrypted_Data(const char* timestamp, const char* raw_data) {
    // 1. 加密原始数据
    // 2. 转换为十六进制格式
    // 3. 调用Write_HideData()
}
```

通过以上详细的函数说明和使用示例，开发者可以快速理解SD卡系统的工作原理，并基于现有代码进行二次开发和功能扩展。
