# 串口协议解析实现方案

## 需求分析

基于老板提供的协议规范，需要实现以下功能：
1. **设备ID管理**: 读取/修改设备ID
2. **单次采集**: 响应单次数据采集请求
3. **连续采集**: 启动/停止连续数据采集
4. **数据格式**: IEEE 754浮点数 + Unix时间戳
5. **CRC校验**: CRC-16/MODBUS校验

## 协议格式分析

### 报文结构
```
[设备ID(2字节)] [系统报文类型(1字节)] [报文长度(2字节)] [协议版本(1字节)] [报文内容(N字节)] [CRC校验(2字节)]
```

### 系统报文类型
- `0x01`: 修改设备ID
- `0x02`: 获取设备ID / 应答
- `0x21`: 单次读取通道数据
- `0x22`: 连续读取通道数据
- `0x2F`: 停止连续读取

### 应答内容
- `0x8000`: 操作成功
- `0x7000~0x7FFF`: 操作失败

## 实现架构

### 1. 协议解析函数
```c
// 主解析函数
void Parse_Protocol_Message(uint8_t* buffer, uint16_t length);

// 报文验证函数
uint8_t Validate_Protocol_Message(uint8_t* buffer, uint16_t length);

// 各命令处理函数
void Handle_Get_Device_ID_Protocol(uint8_t* buffer);
void Handle_Set_Device_ID_Protocol(uint8_t* buffer);
void Handle_Single_Sample_Protocol(uint8_t* buffer);
void Handle_Continuous_Sample_Protocol(uint8_t* buffer);
void Handle_Stop_Sample_Protocol(uint8_t* buffer);
```

### 2. 数据编码函数
```c
// IEEE 754浮点数转换
uint32_t Float_To_IEEE754(float value);
float IEEE754_To_Float(uint32_t ieee_value);

// Unix时间戳转换
uint32_t RTC_To_Unix_Timestamp(rtc_parameter_struct* rtc_time);

// 报文构建函数
uint16_t Build_Response_Message(uint8_t* buffer, uint16_t device_id, 
                               uint8_t msg_type, uint8_t* content, uint16_t content_len);
```

### 3. CRC校验集成
```c
// 使用现有CRC_APP.c中的函数
uint16_t Calculate_CRC16(uint8_t *data, uint16_t length);

// 报文CRC验证
uint8_t Verify_Message_CRC(uint8_t* buffer, uint16_t length);

// 添加CRC到报文
void Add_CRC_To_Message(uint8_t* buffer, uint16_t length);
```

## 关键技术点

### 1. 字节序处理
- 协议使用小端序传输
- 需要处理多字节数据的字节序转换

### 2. IEEE 754格式
- 使用联合体实现浮点数与32位整数的转换
- 确保数据格式的准确性

### 3. 时间戳处理
- 将RTC时间转换为Unix时间戳
- 考虑时区偏移(GMT+8)

### 4. 错误处理
- CRC校验失败处理
- 无效命令处理
- 设备ID不匹配处理

## 集成方案

### 1. 修改现有RS485_Task函数
在现有的字符串命令解析基础上，添加二进制协议解析：

```c
void RS485_Task(void)
{
    if((rs485_rx_index > 0 && (uwTick - rs485_rx_ticks >= 20)) || rs485_rx_idle_flag)
    {
        rs485_rx_idle_flag = 0;
        
        // 首先尝试二进制协议解析
        if(rs485_rx_index >= 8) // 最小协议长度
        {
            if(Validate_Protocol_Message(rs485_rx_buffer, rs485_rx_index))
            {
                Parse_Protocol_Message(rs485_rx_buffer, rs485_rx_index);
                goto cleanup;
            }
        }
        
        // 如果不是二进制协议，继续原有的字符串命令解析
        rs485_rx_buffer[rs485_rx_index] = '\0';
        // ... 原有的字符串命令解析代码 ...
        
cleanup:
        memset(rs485_rx_buffer, 0, rs485_rx_index);
        rs485_rx_index = 0;
    }
}
```

### 2. 数据结构定义
```c
// 协议报文头结构
typedef struct {
    uint16_t device_id;
    uint8_t msg_type;
    uint16_t msg_length;
    uint8_t protocol_version;
} protocol_header_t;

// IEEE 754转换联合体
typedef union {
    float f;
    uint32_t u;
} float_ieee754_t;
```

## 实现优先级

1. **高优先级**: 
   - 协议解析框架
   - CRC校验集成
   - 设备ID管理

2. **中优先级**:
   - 单次采集响应
   - IEEE 754数据格式

3. **低优先级**:
   - 连续采集功能
   - 错误处理优化

## 测试方案

1. **单元测试**: 各个解析函数的独立测试
2. **集成测试**: 完整协议流程测试
3. **兼容性测试**: 确保不影响现有字符串命令功能

## 代码复用

充分利用现有代码：
- CRC_APP.c中的CRC计算函数
- Usart_APP.c中的设备ID管理
- ADC_APP.c中的数据采集功能
- RTC_APP.c中的时间管理功能
