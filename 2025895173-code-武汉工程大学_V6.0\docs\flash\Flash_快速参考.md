# Flash存储系统快速参考

## 基本信息
- **Flash芯片ID**: 0xC84013
- **页大小**: 256字节
- **扇区大小**: 4096字节 (4KB)
- **SPI接口**: SPI1 (PB12-CS, PB13-CLK, PB14-MISO, PB15-MOSI)

## 存储区域映射

| 区域 | 地址 | 大小 | 用途 |
|------|------|------|------|
| 设备ID | 0x000000 | 30B | 设备标识符 |
| 配置区 | 0x001000 | 20B | 系统配置 |
| 日志缓存 | 0x003000 | 512B | 临时日志 |

## 常用函数

### 初始化
```c
void Flash_Init(void);                    // Flash系统初始化
void spi_flash_init(void);               // SPI驱动初始化
uint32_t spi_flash_read_id(void);        // 读取Flash ID
```

### 基础操作
```c
// 读取数据
void spi_flash_buffer_read(uint8_t* pbuffer, uint32_t read_addr, uint16_t num_byte_to_read);

// 写入数据 (写前必须擦除)
void spi_flash_buffer_write(uint8_t* pbuffer, uint32_t write_addr, uint32_t num_byte_to_write);

// 擦除扇区
void spi_flash_sector_erase(uint32_t sector_addr);

// 等待写完成
void spi_flash_wait_for_write_end(void);
```

### 配置管理
```c
// 保存配置 (变比, 阈值, 采样间隔)
uint8_t Save_Config_To_Flash(float ratio, float limit, uint32_t interval);

// 读取配置
uint8_t Read_Config_From_Flash(float* ratio, float* limit, uint32_t* interval);
```

### 日志缓存
```c
// 缓存日志到Flash
uint8_t Cache_Log_To_Flash(const char* log_msg);

// 检查是否有缓存
uint8_t Check_Cached_Log_In_Flash(void);

// 写入缓存到SD卡
uint8_t Write_Cached_Log_To_Log0(void);
```

## 使用示例

### 1. 系统初始化
```c
void system_init(void) {
    Flash_Init();  // 自动完成所有初始化工作
}
```

### 2. 保存系统配置
```c
// 保存变比1.5，阈值50V，采样间隔10秒
if(Save_Config_To_Flash(1.5f, 50.0f, 10000)) {
    printf("配置保存成功\r\n");
}
```

### 3. 读取配置
```c
float ratio, limit;
uint32_t interval;

if(Read_Config_From_Flash(&ratio, &limit, &interval)) {
    Set_Ratio(ratio);
    Set_Limit(limit);
    Set_Sample_Interval(interval);
}
```

### 4. 日志缓存使用
```c
// SD卡不可用时缓存日志
if(!sd_card_available) {
    Cache_Log_To_Flash("系统启动");
}

// SD卡恢复后写入缓存
if(sd_card_available && Check_Cached_Log_In_Flash()) {
    Write_Cached_Log_To_Log0();
}
```

## 重要注意事项

### ⚠️ 写操作规则
1. **写前必须擦除**: Flash只能从1写为0，擦除后全为1
2. **扇区擦除**: 必须按4KB扇区擦除
3. **等待完成**: 写/擦除操作后必须等待完成

### ⚠️ 地址对齐
- **扇区擦除**: 地址必须4KB对齐 (0x000000, 0x001000, 0x002000...)
- **页写入**: 建议256字节对齐以获得最佳性能

### ⚠️ 数据安全
- **校验机制**: 重要数据都有魔数和校验和保护
- **参数验证**: 配置参数有范围检查
- **错误处理**: Flash故障不影响系统运行

## 故障排除

### Flash ID读取失败
```c
uint32_t id = spi_flash_read_id();
if(id != 0xC84013) {
    printf("Flash ID错误: 0x%06X\r\n", id);
    // 检查硬件连接和SPI配置
}
```

### 配置读取失败
```c
if(!Read_Config_From_Flash(&ratio, &limit, &interval)) {
    printf("配置读取失败，使用默认值\r\n");
    // 使用默认配置
}
```

### 写入验证
```c
// 写入后验证
uint8_t test_data[] = "test";
uint8_t read_back[10];

spi_flash_sector_erase(0x010000);
spi_flash_buffer_write(test_data, 0x010000, 4);
spi_flash_buffer_read(read_back, 0x010000, 4);

if(memcmp(test_data, read_back, 4) == 0) {
    printf("写入验证成功\r\n");
}
```

## 配置数据结构

```c
typedef struct {
    uint32_t magic;           // 0x43464749
    float ratio_value;        // 0.0-100.0
    float limit_value;        // 0.0-200.0  
    uint32_t sample_interval; // 1000-15000ms
    uint32_t checksum;        // 校验和
} config_storage_t;
```

## 魔数定义

```c
#define CONFIG_MAGIC_NUMBER    0x43464749  // 配置区魔数
#define LOG_CACHE_MAGIC        0x4C4F4743  // 日志缓存魔数
#define SFLASH_ID              0xC84013    // Flash芯片ID
```

## 地址定义

```c
#define FLASH_DEVICE_ID_ADDR   0x000000    // 设备ID地址
#define CONFIG_STORAGE_ADDR    0x001000    // 配置存储地址
#define LOG_CACHE_ADDR         0x003000    // 日志缓存地址
```

---
*快速参考 v1.0 - 更多详细信息请参考完整文档*
