--cpu=Cortex-M4.fp.sp
".\objects\gd32f4xx_it.o"
".\objects\main.o"
".\objects\systick.o"
".\objects\led.o"
".\objects\oled.o"
".\objects\rtc.o"
".\objects\key.o"
".\objects\sdcard.o"
".\objects\adc.o"
".\objects\timer.o"
".\objects\gd30ad3344.o"
".\objects\usart.o"
".\objects\spi_flash.o"
".\objects\adc_app.o"
".\objects\flash_app.o"
".\objects\function.o"
".\objects\key_app.o"
".\objects\oled_app.o"
".\objects\rtc_app.o"
".\objects\scheduler.o"
".\objects\sdcard_app.o"
".\objects\timer_app.o"
".\objects\usart_app.o"
".\objects\crc_app.o"
".\objects\system_gd32f4xx.o"
".\objects\gd32f4xx_adc.o"
".\objects\gd32f4xx_can.o"
".\objects\gd32f4xx_crc.o"
".\objects\gd32f4xx_ctc.o"
".\objects\gd32f4xx_dac.o"
".\objects\gd32f4xx_dbg.o"
".\objects\gd32f4xx_dci.o"
".\objects\gd32f4xx_dma.o"
".\objects\gd32f4xx_enet.o"
".\objects\gd32f4xx_exmc.o"
".\objects\gd32f4xx_exti.o"
".\objects\gd32f4xx_fmc.o"
".\objects\gd32f4xx_fwdgt.o"
".\objects\gd32f4xx_gpio.o"
".\objects\gd32f4xx_i2c.o"
".\objects\gd32f4xx_ipa.o"
".\objects\gd32f4xx_iref.o"
".\objects\gd32f4xx_misc.o"
".\objects\gd32f4xx_pmu.o"
".\objects\gd32f4xx_rcu.o"
".\objects\gd32f4xx_rtc.o"
".\objects\gd32f4xx_sdio.o"
".\objects\gd32f4xx_spi.o"
".\objects\gd32f4xx_syscfg.o"
".\objects\gd32f4xx_timer.o"
".\objects\gd32f4xx_tli.o"
".\objects\gd32f4xx_trng.o"
".\objects\gd32f4xx_usart.o"
".\objects\gd32f4xx_wwdgt.o"
".\objects\startup_gd32f450_470.o"
".\objects\ff.o"
".\objects\diskio.o"
--library_type=microlib --strict --scatter ".\Objects\CIMC_GD32_Template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\CIMC_GD32_Template.map" -o .\Objects\CIMC_GD32_Template.axf