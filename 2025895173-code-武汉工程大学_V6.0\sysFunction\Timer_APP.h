#ifndef __TIMER_APP_H
#define __TIMER_APP_H

#include "HeaderFiles.h"

// LED闪烁状态枚举
typedef enum {
    LED_BLINK_OFF = 0,      // 不闪烁
    LED_BLINK_ON            // 1s周期闪烁
} led_blink_mode_t;

// ADC采样状态枚举
typedef enum {
    ADC_SAMPLING_OFF = 0,   // 停止采样
    ADC_SAMPLING_ON         // 开始采样
} adc_sampling_state_t;

void Set_LED1_Blink_Mode(led_blink_mode_t mode);        
void Set_LED2_State(uint8_t state);                     
void Set_ADC_Sampling_State(adc_sampling_state_t state);
void Set_ADC_Sample_Interval(uint32_t interval_ms);     
void Timer_LED_Handler(void);                           
void Timer_ADC_Handler(void);                           

#endif
