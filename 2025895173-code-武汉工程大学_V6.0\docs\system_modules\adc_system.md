# ADC系统详细文档

## 模块概述

ADC系统是项目的核心数据采集模块，采用双ADC架构设计：内置12位ADC用于基础采集，外置24位GD30AD3344高精度ADC用于精密测量。该系统支持电压、电流、电阻三种测量模式，具备高精度校准算法，可实现工业级测量精度。

### 主要功能
- **双ADC架构**: 内置12位ADC + 外置24位高精度ADC
- **多测量模式**: 支持电压、电流、电阻三种测量模式
- **高精度校准**: 基于最小二乘法的多项式校准算法
- **实时采样**: 支持连续采样和单次采样模式
- **数据记录**: 自动记录采样数据和超限数据
- **状态管理**: 完整的采样状态和系统状态管理

### 技术规格
- **内置ADC**: 12位分辨率，0-3.3V测量范围
- **外置ADC**: 24位分辨率，GD30AD3344芯片
- **测量精度**: 电压±0.08%，电流±0.3%，电阻±0.264%
- **采样速率**: 可配置采样间隔(1-15秒)
- **通信接口**: SPI0 (PA4-CS, PA5-CLK, PA6-MISO, PA7-MOSI)

## 核心数据结构

### ADC状态枚举
```c
typedef enum {
    ADC_STATE_IDLE = 0,     // 空闲状态
    ADC_STATE_SAMPLING,     // 采样状态
    ADC_STATE_STOPPED       // 停止状态
} adc_state_t;
```

### 系统状态枚举
```c
typedef enum {
    SYSTEM_STATE_IDLE = 0,    // 系统空闲状态
    SYSTEM_STATE_SAMPLING,    // 系统采样状态
    SYSTEM_STATE_CONFIG       // 系统配置状态
} system_state_t;
```

### ADC数据结构
```c
typedef struct {
    uint16_t raw_value;     // ADC原始值
    float voltage;          // 计算后的电压值
    uint32_t timestamp;     // 时间戳
} adc_data_t;
```

### GD30AD3344通道枚举
```c
typedef enum {
    GD30AD3344_Channel_0 = 0,  // AIN0~AIN1 (差分)
    GD30AD3344_Channel_1 = 1,  // AIN0~AIN3 (差分)
    GD30AD3344_Channel_2 = 2,  // AIN1~AIN3 (差分)
    GD30AD3344_Channel_3 = 3,  // AIN2~AIN3 (差分)
    GD30AD3344_Channel_4 = 4,  // AIN0~GND (单端)
    GD30AD3344_Channel_5 = 5,  // AIN1~GND (单端)
    GD30AD3344_Channel_6 = 6,  // AIN2~GND (单端)
    GD30AD3344_Channel_7 = 7,  // AIN3~GND (单端)
} GD30AD3344_Channel_TypeDef;
```

### PGA增益枚举
```c
typedef enum {
    GD30AD3344_PGA_6V144 = 0,  // ±6.144V
    GD30AD3344_PGA_4V096 = 1,  // ±4.096V
    GD30AD3344_PGA_2V048 = 2,  // ±2.048V
    GD30AD3344_PGA_1V024 = 3,  // ±1.024V
    GD30AD3344_PGA_0V512 = 4,  // ±0.512V
    GD30AD3344_PGA_0V256 = 5,  // ±0.256V
    GD30AD3344_PGA_0V064 = 6,  // ±0.064V
} GD30AD3344_PGA_TypeDef;
```

### 重要宏定义
```c
#define ADC_CHANNEL_COUNT 1                    // 内置ADC通道数
#define ADC_GPIO_PORT GPIOC                    // ADC GPIO端口
#define ADC_PIN_CH0 GPIO_PIN_0                 // PC0 -> ADC0_IN10
#define ADC_RESOLUTION_12BIT ADC_RESOLUTION_12B // 12位分辨率
#define ADC_SAMPLE_TIME ADC_SAMPLETIME_480     // 480时钟周期采样时间
```

### 全局变量
```c
extern float ch0_ratio, ch1_ratio, ch2_ratio;     // 各通道变比值
extern float ch0_limit, ch1_limit, ch2_limit;     // 各通道限制值
extern float ch0_data, ch1_data, ch2_data;        // 各通道测量数据
extern adc_state_t adc_state;                     // ADC状态
extern system_state_t system_state;               // 系统状态
extern uint32_t sample_interval;                  // 采样间隔
extern uint8_t read_mode;                         // 读取模式(0-电压,1-电流,2-电阻)
```

## 函数详细说明

### 1. 系统初始化函数

#### ADC_Init()
```c
void ADC_Init(void);
```
**功能**: 内置ADC系统初始化
**参数**: 无
**返回值**: 无
**作用**: 
- 配置ADC时钟(PCLK2/4)
- 配置GPIO为模拟输入模式(PC0)
- 配置ADC0为12位分辨率，右对齐
- 配置采样通道和采样时间
- 使能并校准ADC
**使用场景**: 系统启动时调用，初始化内置ADC硬件

#### gd30ad3344_init()
```c
void gd30ad3344_init(void);
```
**功能**: GD30AD3344高精度ADC初始化
**参数**: 无
**返回值**: 无
**作用**: 
- 使能相关时钟(GPIO、SPI、DMA)
- 配置SPI0接口(PA4-CS, PA5-CLK, PA6-MISO, PA7-MOSI)
- 配置DMA传输参数
- 初始化GD30AD3344配置结构体
- 发送初始化配置到芯片
**使用场景**: 系统启动时调用，初始化外置高精度ADC

### 2. 数据采集函数

#### ADC_Read()
```c
uint16_t ADC_Read(void);
```
**功能**: 读取内置ADC转换结果
**参数**: 无
**返回值**: ADC转换值(0-4095)
**作用**: 
- 启动软件触发转换
- 等待转换完成
- 读取转换结果
- 清除转换完成标志
**使用场景**: 需要读取内置ADC数据时调用

#### GD30AD3344_AD_Read()
```c
float GD30AD3344_AD_Read(GD30AD3344_Channel_TypeDef CH, GD30AD3344_PGA_TypeDef Ref);
```
**功能**: 读取GD30AD3344高精度ADC数据
**参数**: 
- `CH`: ADC通道选择
- `Ref`: PGA增益选择
**返回值**: 转换后的电压值(浮点数)
**作用**: 
- 配置ADC通道和增益
- 通过SPI DMA发送配置并读取数据
- 将原始数据转换为实际电压值
- 公式: result = raw_data * PGA_voltage / 32768
**使用场景**: 需要高精度测量时调用

#### ADC_Proc()
```c
void ADC_Proc(void);
```
**功能**: ADC处理主函数，根据模式进行数据采集
**参数**: 无
**返回值**: 无
**作用**: 
- 根据read_mode选择测量模式(0-电压,1-电流,2-电阻)
- 调用对应的高精度校准函数
- 更新数据有效性标志
- 触发OLED显示更新
**使用场景**: 在任务调度器中周期性调用

### 3. 高精度校准函数

#### CH0_Voltage_Calibration()
```c
float CH0_Voltage_Calibration(float measured_voltage);
```
**功能**: 电压通道高精度校准
**参数**: 
- `measured_voltage`: GD30AD3344测量的原始电压值
**返回值**: 校准后的真实电压值
**作用**: 
- 使用线性校准公式: 真实电压 = 5.061669 × 测量电压 + (-0.171986)
- 基于最小二乘法计算，R²=0.999998
- 精度从±3.3%提升到±0.08%(40倍提升)
**使用场景**: 电压测量模式下的数据校准

#### CH1_Current_Calibration()
```c
float CH1_Current_Calibration(float measured_current);
```
**功能**: 电流通道高精度校准(4-20mA)
**参数**: 
- `measured_current`: GD30AD3344测量的原始电流值
**返回值**: 校准后的真实电流值
**作用**: 
- 使用线性校准公式: 真实电流 = 11.086168 × 测量电流 + (-0.404067)
- 基于最小二乘法计算，R²=0.999963
- 精度从±2.6%提升到±0.3%(9倍提升)
**使用场景**: 电流测量模式下的数据校准

#### CH2_Resistance_Calibration()
```c
float CH2_Resistance_Calibration(float measured_resistance);
```
**功能**: 电阻通道极高精度校准(100Ω-100kΩ)
**参数**: 
- `measured_resistance`: GD30AD3344测量的原始电阻值
**返回值**: 校准后的真实电阻值
**作用**: 
- 使用8次多项式校准，基于26个高密度校准点
- R²=0.999995，精度±0.264%
- 精度从±93%提升到±0.264%(350倍提升)
**使用场景**: 电阻测量模式下的数据校准

### 4. 采样控制函数

#### Start_Sampling()
```c
void Start_Sampling(void);
```
**功能**: 启动数据采样
**参数**: 无
**返回值**: 无
**作用**: 
- 设置ADC状态为SAMPLING
- 设置系统状态为SAMPLING
- 启动定时器采样
- 创建采样数据文件
- 更新LED状态指示
**使用场景**: 接收到开始采样命令时调用

#### Stop_Sampling()
```c
void Stop_Sampling(void);
```
**功能**: 停止数据采样
**参数**: 无
**返回值**: 无
**作用**: 
- 设置ADC状态为STOPPED
- 设置系统状态为IDLE
- 停止定时器采样
- 关闭采样和超限数据文件
- 更新LED状态指示
**使用场景**: 接收到停止采样命令时调用

### 5. 参数配置函数

#### Set_Ratio()
```c
void Set_Ratio(float ratio);
```
**功能**: 设置变比值
**参数**: 
- `ratio`: 变比值(0.0-100.0)
**返回值**: 无
**作用**: 
- 验证参数范围
- 设置当前变比值
**使用场景**: 通过串口命令或配置界面设置变比

#### Get_Ratio()
```c
float Get_Ratio(void);
```
**功能**: 获取当前变比值
**参数**: 无
**返回值**: 当前变比值
**作用**: 返回当前设置的变比值
**使用场景**: 查询当前配置或数据计算时调用

#### Set_Limit()
```c
void Set_Limit(float limit);
```
**功能**: 设置阈值
**参数**: 
- `limit`: 阈值(0.0-200.0)
**返回值**: 无
**作用**: 
- 验证参数范围
- 设置当前阈值
**使用场景**: 通过串口命令或配置界面设置阈值

#### Get_Limit()
```c
float Get_Limit(void);
```
**功能**: 获取当前阈值
**参数**: 无
**返回值**: 当前阈值
**作用**: 返回当前设置的阈值
**使用场景**: 查询当前配置或超限检测时调用

#### Set_Sample_Interval()
```c
void Set_Sample_Interval(uint32_t interval_ms);
```
**功能**: 设置采样间隔
**参数**: 
- `interval_ms`: 采样间隔(毫秒)
**返回值**: 无
**作用**: 设置采样定时器的间隔时间
**使用场景**: 配置采样频率时调用

#### Get_Sample_Interval()
```c
uint32_t Get_Sample_Interval(void);
```
**功能**: 获取当前采样间隔
**参数**: 无
**返回值**: 当前采样间隔(毫秒)
**作用**: 返回当前设置的采样间隔
**使用场景**: 查询当前采样配置时调用

### 6. 状态查询函数

#### Get_ADC_State()
```c
adc_state_t Get_ADC_State(void);
```
**功能**: 获取ADC状态
**参数**: 无
**返回值**: 当前ADC状态
**作用**: 返回当前ADC的工作状态
**使用场景**: 状态查询和逻辑判断时调用

#### Get_System_State()
```c
system_state_t Get_System_State(void);
```
**功能**: 获取系统状态
**参数**: 无
**返回值**: 当前系统状态
**作用**: 返回当前系统的工作状态
**使用场景**: 系统状态查询和界面显示时调用

#### Get_Latest_Data()
```c
adc_data_t Get_Latest_Data(void);
```
**功能**: 获取最新的ADC数据
**参数**: 无
**返回值**: 最新的ADC数据结构
**作用**: 返回最近一次采集的完整数据
**使用场景**: 需要获取最新测量结果时调用

### 7. 数据转换函数

#### ADC_To_Voltage()
```c
float ADC_To_Voltage(uint16_t adc_value);
```
**功能**: 将ADC原始值转换为电压值
**参数**: 
- `adc_value`: ADC原始值(0-4095)
**返回值**: 对应的电压值(0-3.3V)
**作用**: 
- 使用公式: voltage = (adc_value / 4095) * 3.3
- 将12位ADC值转换为实际电压
**使用场景**: 内置ADC数据处理时调用

## 使用示例

### 基本初始化流程
```c
// 系统启动时的ADC初始化
void system_init(void) {
    // 初始化内置ADC
    ADC_Init();
    
    // 初始化外置高精度ADC
    gd30ad3344_init();
    
    // 设置默认参数
    Set_Ratio(1.0f);
    Set_Limit(100.0f);
    Set_Sample_Interval(5000);  // 5秒采样间隔
    
    printf("ADC系统初始化完成\r\n");
}
```

### 数据采集流程
```c
// 启动数据采集
void start_data_collection(void) {
    // 设置测量模式
    read_mode = 0;  // 0-电压, 1-电流, 2-电阻
    
    // 启动采样
    Start_Sampling();
    
    printf("数据采集已启动\r\n");
}

// 停止数据采集
void stop_data_collection(void) {
    Stop_Sampling();
    printf("数据采集已停止\r\n");
}

// 在任务调度器中的处理
void scheduler_adc_task(void) {
    if(Get_ADC_State() == ADC_STATE_SAMPLING) {
        ADC_Proc();  // 执行数据采集和处理
    }
}
```

### 高精度测量示例
```c
// 高精度电压测量
float measure_voltage_high_precision(void) {
    // 使用GD30AD3344进行测量
    float raw_voltage = GD30AD3344_AD_Read(GD30AD3344_Channel_4, GD30AD3344_PGA_6V144);
    
    // 应用高精度校准
    float calibrated_voltage = CH0_Voltage_Calibration(raw_voltage);
    
    printf("原始电压: %.4fV, 校准后电压: %.4fV\r\n", raw_voltage, calibrated_voltage);
    
    return calibrated_voltage;
}

// 高精度电流测量(4-20mA)
float measure_current_high_precision(void) {
    // 使用GD30AD3344进行测量
    float raw_current = GD30AD3344_AD_Read(GD30AD3344_Channel_5, GD30AD3344_PGA_6V144);
    
    // 应用高精度校准
    float calibrated_current = CH1_Current_Calibration(raw_current);
    
    printf("原始电流: %.4fmA, 校准后电流: %.4fmA\r\n", raw_current, calibrated_current);
    
    return calibrated_current;
}

// 高精度电阻测量
float measure_resistance_high_precision(void) {
    // 使用GD30AD3344进行测量
    float raw_resistance = GD30AD3344_AD_Read(GD30AD3344_Channel_6, GD30AD3344_PGA_2V048);
    
    // 应用高精度校准(8次多项式)
    float calibrated_resistance = CH2_Resistance_Calibration(raw_resistance);
    
    printf("原始电阻: %.2fΩ, 校准后电阻: %.2fΩ\r\n", raw_resistance, calibrated_resistance);
    
    return calibrated_resistance;
}
```

### 参数配置示例
```c
// 配置测量参数
void configure_measurement_parameters(void) {
    // 设置各通道变比
    ch0_ratio = 1.5f;   // 电压通道变比
    ch1_ratio = 2.0f;   // 电流通道变比
    ch2_ratio = 1.0f;   // 电阻通道变比
    
    // 设置各通道阈值
    ch0_limit = 50.0f;  // 电压阈值50V
    ch1_limit = 15.0f;  // 电流阈值15mA
    ch2_limit = 10000.0f; // 电阻阈值10kΩ
    
    // 设置采样间隔
    Set_Sample_Interval(2000);  // 2秒采样一次
    
    printf("测量参数配置完成\r\n");
}

// 查询当前配置
void query_current_configuration(void) {
    printf("当前配置:\r\n");
    printf("变比: %.2f\r\n", Get_Ratio());
    printf("阈值: %.2f\r\n", Get_Limit());
    printf("采样间隔: %dms\r\n", Get_Sample_Interval());
    printf("ADC状态: %d\r\n", Get_ADC_State());
    printf("系统状态: %d\r\n", Get_System_State());
}
```

### 数据记录和超限检测
```c
// 数据记录和超限检测
void process_measurement_data(void) {
    float voltage, current, resistance;
    char timestamp[32];
    
    // 获取当前时间戳
    get_current_timestamp(timestamp);
    
    // 根据测量模式获取数据
    switch(read_mode) {
        case 0:  // 电压模式
            voltage = measure_voltage_high_precision();
            
            // 记录正常数据
            Write_Sample_Data(timestamp, voltage, ch0_ratio);
            
            // 检查是否超限
            if(voltage > ch0_limit) {
                Write_OverLimit_Data(timestamp, voltage, ch0_limit);
                printf("电压超限: %.2fV > %.2fV\r\n", voltage, ch0_limit);
            }
            break;
            
        case 1:  // 电流模式
            current = measure_current_high_precision();
            
            // 记录和检测逻辑类似
            break;
            
        case 2:  // 电阻模式
            resistance = measure_resistance_high_precision();
            
            // 记录和检测逻辑类似
            break;
    }
}
```

## 注意事项

### 1. 硬件连接要求
- **内置ADC**: PC0必须连接到待测信号，注意电压范围0-3.3V
- **外置ADC**: SPI0接口连接正确，CS信号控制时序
- **参考电压**: 确保VREF稳定，影响测量精度
- **信号调理**: 输入信号需要适当的调理电路

### 2. 测量精度优化
- **校准算法**: 使用对应的高精度校准函数
- **温度补偿**: 考虑温度对测量精度的影响
- **噪声抑制**: 适当的滤波和屏蔽措施
- **采样时间**: 足够的采样时间确保稳定

### 3. 数据处理注意事项
- **数据有效性**: 检查数据有效性标志
- **超限处理**: 及时处理超限数据和报警
- **数据存储**: 确保数据正确存储到SD卡
- **实时性**: 平衡测量精度和实时性要求

### 4. 系统集成考虑
- **任务调度**: 合理安排ADC任务的执行频率
- **中断处理**: 避免在ADC转换期间响应其他中断
- **电源管理**: 确保ADC电源稳定，避免干扰
- **错误恢复**: 建立完善的错误检测和恢复机制

## 二次开发指南

### 1. 添加新的测量通道
```c
// 扩展测量通道
#define CH3_TEMPERATURE_CHANNEL  GD30AD3344_Channel_7

float measure_temperature(void) {
    float raw_temp = GD30AD3344_AD_Read(CH3_TEMPERATURE_CHANNEL, GD30AD3344_PGA_2V048);
    
    // 温度校准算法
    float temperature = temperature_calibration(raw_temp);
    
    return temperature;
}

// 添加到ADC_Proc()中
void ADC_Proc_Extended(void) {
    switch(read_mode) {
        case 0: // 电压
        case 1: // 电流  
        case 2: // 电阻
            // 原有逻辑
            break;
        case 3: // 温度
            ch3_data = measure_temperature();
            break;
    }
}
```

### 2. 实现自动量程切换
```c
// 自动量程切换
typedef struct {
    GD30AD3344_PGA_TypeDef current_range;
    float max_value;
    float min_value;
} auto_range_t;

GD30AD3344_PGA_TypeDef auto_select_range(float estimated_value) {
    if(estimated_value > 4.0f) return GD30AD3344_PGA_6V144;
    else if(estimated_value > 2.0f) return GD30AD3344_PGA_4V096;
    else if(estimated_value > 1.0f) return GD30AD3344_PGA_2V048;
    else return GD30AD3344_PGA_1V024;
}

float measure_with_auto_range(GD30AD3344_Channel_TypeDef channel) {
    // 1. 快速测量估算值
    // 2. 选择最佳量程
    // 3. 重新测量获得精确值
}
```

### 3. 实现数据滤波算法
```c
// 数字滤波器
typedef struct {
    float buffer[10];
    uint8_t index;
    uint8_t count;
} moving_average_filter_t;

float apply_moving_average(moving_average_filter_t* filter, float new_value) {
    filter->buffer[filter->index] = new_value;
    filter->index = (filter->index + 1) % 10;
    
    if(filter->count < 10) filter->count++;
    
    float sum = 0;
    for(int i = 0; i < filter->count; i++) {
        sum += filter->buffer[i];
    }
    
    return sum / filter->count;
}

// 卡尔曼滤波器
typedef struct {
    float x;      // 状态估计
    float P;      // 误差协方差
    float Q;      // 过程噪声
    float R;      // 测量噪声
} kalman_filter_t;

float kalman_filter(kalman_filter_t* kf, float measurement) {
    // 预测步骤
    kf->P = kf->P + kf->Q;
    
    // 更新步骤
    float K = kf->P / (kf->P + kf->R);
    kf->x = kf->x + K * (measurement - kf->x);
    kf->P = (1 - K) * kf->P;
    
    return kf->x;
}
```

### 4. 实现多点校准系统
```c
// 多点校准数据结构
typedef struct {
    float reference_values[20];
    float measured_values[20];
    uint8_t point_count;
    float coefficients[9];  // 多项式系数
} calibration_data_t;

uint8_t Add_Calibration_Point(calibration_data_t* cal, float reference, float measured) {
    if(cal->point_count >= 20) return 0;
    
    cal->reference_values[cal->point_count] = reference;
    cal->measured_values[cal->point_count] = measured;
    cal->point_count++;
    
    return 1;
}

uint8_t Calculate_Calibration_Coefficients(calibration_data_t* cal) {
    // 使用最小二乘法计算多项式系数
    // 实现矩阵运算求解系数
}

float Apply_Calibration(calibration_data_t* cal, float measured_value) {
    // 应用多项式校准
    float result = 0;
    float x_power = 1;
    
    for(int i = 0; i < 9; i++) {
        result += cal->coefficients[i] * x_power;
        x_power *= measured_value;
    }
    
    return result;
}
```

### 5. 实现数据统计分析
```c
// 统计分析结构
typedef struct {
    float min_value;
    float max_value;
    float sum;
    float sum_squares;
    uint32_t count;
} statistics_t;

void Update_Statistics(statistics_t* stats, float new_value) {
    if(stats->count == 0) {
        stats->min_value = new_value;
        stats->max_value = new_value;
    } else {
        if(new_value < stats->min_value) stats->min_value = new_value;
        if(new_value > stats->max_value) stats->max_value = new_value;
    }
    
    stats->sum += new_value;
    stats->sum_squares += new_value * new_value;
    stats->count++;
}

float Get_Average(statistics_t* stats) {
    return stats->count > 0 ? stats->sum / stats->count : 0;
}

float Get_Standard_Deviation(statistics_t* stats) {
    if(stats->count < 2) return 0;
    
    float mean = Get_Average(stats);
    float variance = (stats->sum_squares - stats->count * mean * mean) / (stats->count - 1);
    
    return sqrt(variance);
}
```

通过以上详细的函数说明和使用示例，开发者可以全面理解ADC系统的工作原理，并基于现有代码进行高精度测量功能的扩展和优化。
