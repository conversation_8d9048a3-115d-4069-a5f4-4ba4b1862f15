#include "scheduler.h"

uint8_t task_num;

void test_task()
{
//    rs485_printf("ch0:%.2f\r\n", ch0_ratio);
//    rs485_printf("ch1:%.2f\r\n", ch1_ratio);
//    rs485_printf("ch2:%.2f\r\n", ch2_ratio);
}

typedef struct
{
	void(*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}task_t;

static task_t scheduler_task[] =
{
    {Key_Proc, 5, 0},
    //{Uart_Task, 10, 0},
    //{Oled_Task, 300, 0},
    {RS485_Task, 5, 0},
    //{test_task, 2000, 0},
	{ADC_Proc, 500, 0},
};

void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = uwTick;
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            scheduler_task[i].last_run = now_time;
            scheduler_task[i].task_func();
        }
    }
}

