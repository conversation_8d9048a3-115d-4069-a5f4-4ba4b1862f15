#include "Key.h"

/**
 * @brief 按键初始化
 */
void Key_Init(void)
{
	rcu_periph_clock_enable(RCU_GPIOD); // 启用GPIOD时钟
	gpio_mode_set(KEY1_PORT, GPIO_MODE_INPUT, GPIO_PUPD_PULLUP, KEY1_PIN | KEY2_PIN | KEY3_PIN | KEY4_PIN | KEY5_PIN | KEY6_PIN); // 配置为上拉输入
}

/**
 * @brief 读取按键状态
 * @return uint8_t: 按键状态位掩码 (bit0=KEY1, bit1=KEY2, bit2=KEY3, bit3=KEY4)
 */
uint8_t Key_Read(void)
{
	uint8_t key_state = 0;

	if(gpio_input_bit_get(KEY1_PORT, KEY1_PIN) == RESET) key_state |= KEY1_MASK; // KEY1按下
	if(gpio_input_bit_get(KEY2_PORT, KEY2_PIN) == RESET) key_state |= KEY2_MASK; // KEY2按下
	if(gpio_input_bit_get(KEY3_PORT, KEY3_PIN) == RESET) key_state |= KEY3_MASK; // KEY3按下
	if(gpio_input_bit_get(KEY4_PORT, KEY4_PIN) == RESET) key_state |= KEY4_MASK; // KEY4按下
	if(gpio_input_bit_get(KEY5_PORT, KEY5_PIN) == RESET) key_state |= KEY5_MASK; // KEY3按下
	if(gpio_input_bit_get(KEY6_PORT, KEY6_PIN) == RESET) key_state |= KEY6_MASK; // KEY4按下
	
	return key_state;
}

