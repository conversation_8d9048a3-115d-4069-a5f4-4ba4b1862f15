# RTC系统详细文档

## 模块概述

RTC(Real Time Clock)系统是项目的核心时间管理模块，基于GD32F470VE内置的RTC外设实现。该系统提供精确的时间计时功能，支持年、月、日、时、分、秒的完整时间管理，并具备掉电保持能力。

### 主要功能
- **实时时间计时**: 提供精确的年月日时分秒计时
- **掉电保持**: 通过备份电池保持时间连续性
- **时间设置**: 支持通过串口命令和程序接口设置时间
- **时间读取**: 提供多种格式的时间读取接口
- **BCD格式支持**: 内部使用BCD格式存储时间数据
- **24小时制**: 支持24小时时间格式显示

### 技术规格
- **时钟源**: 外部32.768KHz晶振(LXTAL)
- **精度**: ±20ppm (约±1.7秒/天)
- **时间范围**: 2000-2099年
- **备份支持**: VBAT域备份电池供电
- **分频器**: 异步分频器(0x7F) + 同步分频器(0xFF)

## 核心数据结构

### RTC参数结构体
```c
typedef struct {
    uint32_t factor_asyn;     // 异步分频因子 (0x7F)
    uint32_t factor_syn;      // 同步分频因子 (0xFF)
    uint32_t year;           // 年份 (BCD格式, 00-99)
    uint32_t month;          // 月份 (BCD格式, 01-12)
    uint32_t date;           // 日期 (BCD格式, 01-31)
    uint32_t hour;           // 小时 (BCD格式, 00-23)
    uint32_t minute;         // 分钟 (BCD格式, 00-59)
    uint32_t second;         // 秒钟 (BCD格式, 00-59)
    uint32_t day_of_week;    // 星期 (1-7)
    uint32_t display_format; // 显示格式 (12/24小时制)
    uint32_t am_pm;          // 上午/下午标识
} rtc_parameter_struct;
```

### 重要宏定义
```c
#define BKP_VALUE           0x32F0      // 备份寄存器标识值
#define RTC_CLOCK_SOURCE_LXTAL          // 强制使用外部晶振
#define RTC_24HOUR          0x00000000  // 24小时制
#define RTC_AM              0x00000000  // 上午标识
```

### 全局变量
```c
extern rtc_parameter_struct rtc_initpara;  // RTC参数结构
extern uint32_t prescaler_a;               // 异步分频器值
extern uint32_t prescaler_s;               // 同步分频器值
extern uint32_t RTCSRC_FLAG;              // RTC时钟源标志
```

## 函数详细说明

### 1. 系统初始化函数

#### RTC_Init()
```c
void RTC_Init(void);
```
**功能**: RTC系统完整初始化
**参数**: 无
**返回值**: 无
**作用**: 
- 使能PMU时钟和备份域写访问
- 配置备份域LDO为开启状态
- 等待LDO准备就绪
- 调用rtc_pre_config()进行预配置
- 检查RTC是否已配置，未配置则使用默认时间
- 清除复位标志
**使用场景**: 系统启动时调用，建立完整的RTC时间系统

#### rtc_pre_config()
```c
void rtc_pre_config(void);
```
**功能**: RTC预配置，设置时钟源和分频器
**参数**: 无
**返回值**: 无
**作用**: 
- 设置LXTAL驱动能力为最高
- 启动外部32.768KHz晶振
- 等待晶振稳定(超时保护)
- 配置RTC时钟源为LXTAL
- 设置分频器参数(异步0x7F，同步0xFF)
- 使能RTC时钟并等待同步
**使用场景**: RTC_Init()内部调用，配置硬件时钟源

#### rtc_setup_default()
```c
void rtc_setup_default(void);
```
**功能**: 使用默认时间设置RTC
**参数**: 无
**返回值**: 无
**作用**: 
- 设置默认时间为2025-01-01 00:00:00
- 配置24小时制显示格式
- 初始化RTC寄存器
- 保存配置标志到备份寄存器
**使用场景**: 首次使用或备份数据丢失时自动调用

### 2. 时间设置函数

#### Configure_RTC_With_DateTime()
```c
uint8_t Configure_RTC_With_DateTime(uint16_t* datetime);
```
**功能**: 使用日期时间数组配置RTC
**参数**: 
- `datetime`: 日期时间数组，格式为[年, 月, 日, 时, 分, 秒]
**返回值**: 
- `0`: 配置成功
- `1`: 参数错误
- `2`: 配置失败
**作用**: 
- 验证输入参数有效性
- 检查日期时间范围(年:2000-2099, 月:1-12, 日:1-31, 时:0-23, 分秒:0-59)
- 将十进制值转换为BCD格式
- 配置RTC参数结构体
- 初始化RTC寄存器
- 保存配置标志
**使用场景**: 通过串口命令或程序接口设置系统时间

#### convert_to_bcd()
```c
static uint8_t convert_to_bcd(uint8_t value);
```
**功能**: 将十进制值转换为BCD格式
**参数**: 
- `value`: 十进制数值(0-99)
**返回值**: BCD格式的数值
**作用**: 
- 将十进制数的十位和个位分别存储在高4位和低4位
- 例如: 25 -> 0x25 (BCD格式)
**使用场景**: 设置RTC时间前的数据格式转换

#### rtc_setup()
```c
void rtc_setup(void);
```
**功能**: 通过串口交互式设置RTC时间
**参数**: 无
**返回值**: 无
**作用**: 
- 临时禁用串口中断避免冲突
- 通过串口提示用户输入年、月、日、时、分、秒
- 验证输入数据的有效性
- 配置RTC时间
- 显示设置结果
- 重新启用串口中断
**使用场景**: 调试模式下手动设置系统时间

### 3. 时间读取函数

#### rtc_current_time_get()
```c
void rtc_current_time_get(rtc_parameter_struct* rtc_initpara_struct);
```
**功能**: 获取当前RTC时间
**参数**: 
- `rtc_initpara_struct`: 返回时间数据的结构体指针
**返回值**: 无
**作用**: 
- 从RTC寄存器读取当前时间
- 将BCD格式时间数据填入结构体
- 包含年、月、日、时、分、秒、星期等完整信息
**使用场景**: 需要获取当前系统时间时调用

#### rtc_show_time()
```c
void rtc_show_time(void);
```
**功能**: 显示当前时间到串口
**参数**: 无
**返回值**: 无
**作用**: 
- 获取当前RTC时间
- 格式化输出时间字符串
- 通过printf输出到串口
- 格式: "Time:20XX-XX-XX XX:XX:XX"
**使用场景**: 调试时查看当前系统时间

#### RTC_Proc()
```c
void RTC_Proc(void);
```
**功能**: RTC处理任务函数
**参数**: 无
**返回值**: 无
**作用**: 
- 调用rtc_show_time()显示当前时间
- 可在任务调度器中周期性调用
**使用场景**: 在主循环或任务调度器中定期显示时间

### 4. 底层硬件函数

#### rtc_init()
```c
ErrStatus rtc_init(rtc_parameter_struct* rtc_initpara_struct);
```
**功能**: 底层RTC寄存器初始化
**参数**: 
- `rtc_initpara_struct`: RTC配置参数结构体
**返回值**: 
- `SUCCESS`: 初始化成功
- `ERROR`: 初始化失败
**作用**: 
- 禁用写保护
- 进入初始化模式
- 配置分频器和时间寄存器
- 设置显示格式
- 退出初始化模式并等待同步
- 重新启用写保护
**使用场景**: 上层时间设置函数内部调用

#### rtc_register_sync_wait()
```c
ErrStatus rtc_register_sync_wait(void);
```
**功能**: 等待RTC寄存器同步
**参数**: 无
**返回值**: 
- `SUCCESS`: 同步成功
- `ERROR`: 同步超时
**作用**: 
- 等待RTC_TIME和RTC_DATE寄存器与APB时钟同步
- 确保影子寄存器更新完成
- 提供超时保护机制
**使用场景**: RTC配置后确保数据同步

#### usart_input_threshold()
```c
uint8_t usart_input_threshold(uint32_t value);
```
**功能**: 串口输入数值验证
**参数**: 
- `value`: 输入数值的最大允许值
**返回值**: 验证后的BCD格式数值
**作用**: 
- 清除串口接收缓冲区
- 接收两位数字字符
- 验证字符是否为有效数字(0-9)
- 检查数值是否在允许范围内
- 转换为BCD格式返回
**使用场景**: 串口交互式时间设置时的输入验证

## 使用示例

### 基本初始化流程
```c
// 系统启动时的RTC初始化
void system_init(void) {
    // RTC系统初始化
    RTC_Init();  // 自动完成所有初始化工作
    
    // 显示当前时间
    rtc_show_time();
}
```

### 程序设置时间
```c
// 通过程序接口设置时间
void set_system_time(void) {
    uint16_t datetime[6];
    
    // 设置时间为 2025-01-15 14:30:25
    datetime[0] = 2025;  // 年
    datetime[1] = 1;     // 月
    datetime[2] = 15;    // 日
    datetime[3] = 14;    // 时
    datetime[4] = 30;    // 分
    datetime[5] = 25;    // 秒
    
    uint8_t result = Configure_RTC_With_DateTime(datetime);
    if(result == 0) {
        printf("时间设置成功\r\n");
        rtc_show_time();
    } else {
        printf("时间设置失败，错误代码: %d\r\n", result);
    }
}
```

### 读取当前时间
```c
// 获取当前时间用于数据记录
void get_current_timestamp(char* timestamp_str) {
    rtc_parameter_struct current_time;
    
    // 获取当前时间
    rtc_current_time_get(&current_time);
    
    // 格式化时间字符串
    sprintf(timestamp_str, "20%02x-%02x-%02x %02x:%02x:%02x",
            current_time.year, current_time.month, current_time.date,
            current_time.hour, current_time.minute, current_time.second);
}

// 在数据采集中使用时间戳
void record_data_with_timestamp(float voltage) {
    char timestamp[32];
    get_current_timestamp(timestamp);
    
    // 记录数据到SD卡
    Write_Sample_Data(timestamp, voltage, 1.0f);
    
    printf("数据记录: %s, 电压: %.2fV\r\n", timestamp, voltage);
}
```

### 串口命令时间设置
```c
// 处理串口时间设置命令
void handle_rtc_command(char* command) {
    if(strstr(command, "command:set_RTC") != NULL) {
        // 解析时间参数
        uint16_t datetime[6];
        if(parse_datetime_from_command(command, datetime)) {
            uint8_t result = Configure_RTC_With_DateTime(datetime);
            if(result == 0) {
                printf("report:ok\r\n");
                
                // 记录时间设置日志
                char log_msg[100];
                sprintf(log_msg, "RTC set via command to 20%02d-%02d-%02d %02d:%02d:%02d",
                       datetime[0]%100, datetime[1], datetime[2],
                       datetime[3], datetime[4], datetime[5]);
                Write_Log_Data(log_msg);
            } else {
                printf("report:error=rtc_config_failed\r\n");
            }
        }
    } else if(strstr(command, "command:get_RTC") != NULL) {
        // 返回当前时间
        rtc_parameter_struct current_time;
        rtc_current_time_get(&current_time);
        
        printf("report:CurrentTime=20%02x-%02x-%02x %02x:%02x:%02x\r\n",
               current_time.year, current_time.month, current_time.date,
               current_time.hour, current_time.minute, current_time.second);
    }
}
```

### 定时任务中的时间显示
```c
// 在任务调度器中定期显示时间
void scheduler_time_task(void) {
    static uint32_t last_time_display = 0;
    
    // 每10秒显示一次时间
    if(uwTick - last_time_display >= 10000) {
        RTC_Proc();  // 显示当前时间
        last_time_display = uwTick;
    }
}
```

## 注意事项

### 1. 硬件配置要求
- **外部晶振**: 必须使用32.768KHz外部晶振，不支持内部RC振荡器
- **备份电池**: 需要连接备份电池(VBAT)以保持掉电时间
- **晶振负载**: 确保晶振负载电容匹配(通常12.5pF)
- **PCB布线**: 晶振走线应尽量短，远离高频信号

### 2. 时间格式注意事项
- **BCD格式**: RTC内部使用BCD格式存储，需要正确转换
- **年份范围**: 支持2000-2099年，输入时只需后两位
- **24小时制**: 系统固定使用24小时制，不支持12小时制
- **闰年处理**: 硬件自动处理闰年，无需软件干预

### 3. 电源管理
- **备份域**: 必须正确配置备份域LDO
- **写保护**: 操作RTC寄存器前必须禁用写保护
- **时钟使能**: 确保PMU和RTC时钟正确使能
- **掉电保持**: 备份电池电压不能低于2.0V

### 4. 同步和时序
- **寄存器同步**: 每次配置后必须等待寄存器同步
- **初始化模式**: 设置时间时必须进入初始化模式
- **超时保护**: 所有等待操作都应有超时保护
- **中断处理**: 避免在RTC配置期间响应其他中断

## 二次开发指南

### 1. 添加闹钟功能
```c
// 扩展RTC功能，添加闹钟支持
typedef struct {
    uint8_t hour;
    uint8_t minute;
    uint8_t second;
    uint8_t enabled;
} rtc_alarm_config_t;

uint8_t Set_RTC_Alarm(rtc_alarm_config_t* alarm) {
    // 1. 配置闹钟时间
    // 2. 使能闹钟中断
    // 3. 设置闹钟回调函数
}

void RTC_Alarm_IRQHandler(void) {
    // 闹钟中断处理函数
    if(rtc_flag_get(RTC_FLAG_ALRM0)) {
        // 处理闹钟事件
        rtc_flag_clear(RTC_FLAG_ALRM0);
    }
}
```

### 2. 实现时间戳转换
```c
// Unix时间戳转换功能
uint32_t RTC_To_Unix_Timestamp(rtc_parameter_struct* rtc_time) {
    // 1. 将BCD格式转换为十进制
    // 2. 计算从1970年1月1日开始的秒数
    // 3. 考虑闰年和时区偏移
}

void Unix_Timestamp_To_RTC(uint32_t timestamp, rtc_parameter_struct* rtc_time) {
    // 1. 计算年月日时分秒
    // 2. 转换为BCD格式
    // 3. 填充RTC结构体
}
```

### 3. 添加时区支持
```c
// 时区管理功能
typedef enum {
    TIMEZONE_UTC = 0,
    TIMEZONE_GMT_PLUS_8 = 8,
    TIMEZONE_GMT_MINUS_5 = -5
} timezone_t;

void Set_Timezone(timezone_t zone) {
    // 设置系统时区偏移
}

void Get_Local_Time(rtc_parameter_struct* local_time) {
    // 1. 获取UTC时间
    // 2. 应用时区偏移
    // 3. 返回本地时间
}
```

### 4. 实现时间校准
```c
// RTC时间校准功能
typedef struct {
    int16_t calibration_value;  // 校准值 (-511 to +512)
    uint8_t calibration_period; // 校准周期
} rtc_calibration_t;

uint8_t Calibrate_RTC(rtc_calibration_t* cal) {
    // 1. 计算校准参数
    // 2. 配置校准寄存器
    // 3. 验证校准效果
}

void Auto_Calibrate_RTC(void) {
    // 1. 与外部时间源比较
    // 2. 计算时间偏差
    // 3. 自动调整校准值
}
```

### 5. 扩展时间显示格式
```c
// 多种时间显示格式
typedef enum {
    TIME_FORMAT_ISO8601,     // 2025-01-15T14:30:25
    TIME_FORMAT_US,          // 01/15/2025 2:30:25 PM
    TIME_FORMAT_CHINESE,     // 2025年1月15日 14时30分25秒
    TIME_FORMAT_COMPACT      // 20250115143025
} time_format_t;

void Format_Time_String(rtc_parameter_struct* rtc_time, 
                        time_format_t format, 
                        char* output_str) {
    switch(format) {
        case TIME_FORMAT_ISO8601:
            sprintf(output_str, "20%02x-%02x-%02xT%02x:%02x:%02x",
                   rtc_time->year, rtc_time->month, rtc_time->date,
                   rtc_time->hour, rtc_time->minute, rtc_time->second);
            break;
        // 其他格式实现...
    }
}
```

### 6. 添加时间事件系统
```c
// 时间事件管理
typedef struct {
    rtc_parameter_struct trigger_time;
    void (*callback)(void);
    uint8_t repeat;
    uint8_t enabled;
} time_event_t;

uint8_t Add_Time_Event(time_event_t* event) {
    // 添加定时事件到事件列表
}

void Process_Time_Events(void) {
    // 在主循环中检查和处理时间事件
    rtc_parameter_struct current_time;
    rtc_current_time_get(&current_time);
    
    // 遍历事件列表，触发到期事件
}
```

通过以上详细的函数说明和使用示例，开发者可以全面理解RTC系统的工作原理，并基于现有代码进行时间管理功能的扩展和优化。
