# 任务15 - 单次采集协议实现文档

## 项目信息
- **实现时间**: 2025-08-11
- **负责工程师**: Alex
- **协议版本**: v1.0
- **集成状态**: 已完成并集成到主协议解析流程

## 功能概述

任务15实现了单次采集协议，允许上位机通过二进制协议命令MCU进行单次三通道数据采集，并返回包含时间戳和IEEE 754格式浮点数据的标准响应。

## 协议规范

### 输入协议格式
```
命令: 000221000801E7B5
解析:
- 设备ID (2字节): 0x0002 (大端序)
- 系统报文类型 (1字节): 0x21 (单次读取通道数据)
- 报文长度 (2字节): 0x0008 (8字节，大端序)
- 协议版本 (1字节): 0x01
- CRC校验 (2字节): 0xE7B5 (小端序)
```

### 输出协议格式
```
响应: 0002010018016890481E4060A3D74164CCCD46959C00DF4F
解析:
- 设备ID (2字节): 0x0002 (大端序)
- 系统报文类型 (1字节): 0x01 (数据)
- 报文长度 (2字节): 0x0018 (24字节，大端序)
- 协议版本 (1字节): 0x01
- 报文内容 (16字节):
  * 时间戳 (4字节): Unix时间戳，大端序
  * 通道0数据 (4字节): IEEE 754浮点数，大端序
  * 通道1数据 (4字节): IEEE 754浮点数，大端序
  * 通道2数据 (4字节): IEEE 754浮点数，大端序
- CRC校验 (2字节): 小端序
```

## 技术实现

### 1. IEEE 754转换函数
```c
uint32_t Float_To_IEEE754(float value)
{
    union {
        float f;
        uint32_t i;
    } converter;
    
    converter.f = value;
    return converter.i;
}
```

### 2. 主处理函数
```c
void Handle_Single_Sample_Protocol(uint8_t* buffer)
```

**功能流程:**
1. 触发单次采集 (复用现有的vol_flag_single等机制)
2. 获取当前RTC时间并转换为Unix时间戳
3. 计算实际值: `实际值 = 原始ADC值 × 变比系数`
4. 将浮点数转换为IEEE 754格式
5. 构建24字节标准响应报文
6. 计算CRC校验并发送

### 3. 协议集成
在`Parse_Protocol_Message`函数中添加了0x21类型的分发:
```c
else if(msg_type == 0x21) // 单次采集 (任务15)
{
    Handle_Single_Sample_Protocol(buffer);
}
```

## 数据处理详解

### 数据源
- **ch0_data**: 通道0原始ADC值 (电压模式)
- **ch1_data**: 通道1原始ADC值 (电流模式)  
- **ch2_data**: 通道2原始ADC值 (电阻模式)

### 变比计算
- **ch0_ratio**: 通道0变比系数 (范围: 0.1-100.0)
- **ch1_ratio**: 通道1变比系数 (范围: 0.1-100.0)
- **ch2_ratio**: 通道2变比系数 (范围: 0.1-100.0)

**计算公式**: `实际值 = 原始ADC值 × 变比系数`

### 时间戳处理
使用现有的`Convert_RTC_To_Unix_Timestamp()`函数将RTC时间转换为Unix时间戳，支持GMT+8时区自动转换。

## 测试验证

### 测试命令
```
输入: 000221000801E7B5
预期输出: 0002010018016890481E4060A3D74164CCCD46959C00DF4F
```

### 数据解析示例
```
时间戳: 6890481E = 2025-01-13 13:41:50 (GMT+0800)
通道0: 4060A3D7 = 3.51 V (IEEE 754)
通道1: 4164CCCD = 14.30 mA (IEEE 754)  
通道2: 46959C00 = 19150 Ω (IEEE 754)
```

## 集成状态

- ✅ IEEE 754转换函数已实现
- ✅ 单次采集处理函数已实现
- ✅ 协议分发已集成到主解析流程
- ✅ 头文件声明已更新
- ✅ 编译检查通过，无语法错误

## 兼容性说明

该实现完全兼容现有的数据采集系统，复用了以下现有组件:
- ADC数据采集机制 (vol_flag_single等)
- 变比配置系统 (ch0_ratio等)
- RTC时间系统 (Convert_RTC_To_Unix_Timestamp)
- CRC校验系统 (Calculate_CRC16)

## 后续扩展

该实现为后续任务(连续采集、停止采集等)提供了良好的基础架构，可以轻松扩展更多协议类型。
