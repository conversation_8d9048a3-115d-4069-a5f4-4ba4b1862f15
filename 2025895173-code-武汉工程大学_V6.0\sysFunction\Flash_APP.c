#include "Flash_APP.h"
#include "Usart_APP.h"
#include "ADC_APP.h"
#include "Oled_APP.h"
#include "Sdcard_APP.h"
#include "Key_APP.h"
#include "ff.h"
#include <string.h>

#define  SFLASH_ID                     0xC84013
#define  FLASH_DEVICE_ID_ADDR          0x000000  // 设备ID存储地址（存储在Flash的起始地址0x000000）
#define  DEVICE_ID_MAX_LENGTH          30        // 设备ID最大长度

uint32_t flash_id = 0;
uint8_t  device_id_buffer[] = "2025-CIMC-2025895173";
uint32_t current_log_id = 0;

extern FATFS fs;

void Flash_Init()
{
	rs485_printf("\r\n====system init====\r\n");

	//初始化SPI Flash
	spi_flash_init();

	// 获取flash ID
	flash_id = spi_flash_read_id();

	// 检查Flash ID是否正确
	if(SFLASH_ID == flash_id)
	{
		uint8_t temp_buffer[DEVICE_ID_MAX_LENGTH];
		spi_flash_buffer_read(temp_buffer, FLASH_DEVICE_ID_ADDR, DEVICE_ID_MAX_LENGTH);
		
		// 如果第一个字节是0xFF，表示Flash未写入数据，写入队伍编号
		if(temp_buffer[0] == 0xFF)
		{
			spi_flash_sector_erase(FLASH_DEVICE_ID_ADDR);
			
			spi_flash_wait_for_write_end();
			
			spi_flash_buffer_write(device_id_buffer, FLASH_DEVICE_ID_ADDR, strlen((char*)device_id_buffer));
		}
		
		uint8_t rx_buffer[DEVICE_ID_MAX_LENGTH];
		spi_flash_buffer_read(rx_buffer, FLASH_DEVICE_ID_ADDR, DEVICE_ID_MAX_LENGTH);

		rs485_printf("Device_ID:");
		for(uint16_t i = 0; i < DEVICE_ID_MAX_LENGTH; i++)
		{
			if(rx_buffer[i] == 0 || rx_buffer[i] == 0xFF)
			{
				break;
			}
			rs485_printf("%c", rx_buffer[i]);
		}
		rs485_printf("\r\n");

		// 尝试从Flash读取配置参数
		float flash_ratio = 0.0f;
		float flash_limit = 0.0f;
		uint32_t flash_interval = 5000;//默认5秒采样间隔
		if(Read_Config_From_Flash(&flash_ratio, &flash_limit, &flash_interval))
		{
			// 读取成功，设置配置
			Set_Ratio(flash_ratio);
			Set_Limit(flash_limit);
			Set_Sample_Interval(flash_interval); // 应用采样间隔配置
			extern sample_period_t current_sample_period;
			current_sample_period = (sample_period_t)(flash_interval / 1000);
		}
		else
		{
			// 读取失败，使用默认配置
			Set_Sample_Interval(5000); //默认5秒采样间隔
			// 更新current_sample_period变量
			extern sample_period_t current_sample_period;
			current_sample_period = SAMPLE_PERIOD_5S;
		}
		Log_Init();
	}
	else
	{
		// Flash ID不匹配，使用默认设备ID
		rs485_printf("Device_ID:2025-CIMC-2025895173\r\n");
	}
	rs485_printf("====system ready====\r\n");
	Set_System_State(SYSTEM_STATE_IDLE);
	// 系统初始化完成，保持OLED空白状态
}

// 计算配置校验和
uint32_t Calculate_Config_Checksum(config_storage_t* data)
{
    uint32_t checksum = 0;
    uint8_t* ptr = (uint8_t*)data;

    // 计算除checksum字段外的所有字节的校验和
    for(int i = 0; i < sizeof(config_storage_t) - sizeof(uint32_t); i++)
    {
        checksum += ptr[i];
    }

    return checksum;
}

// 校验配置数据
uint8_t Verify_Config_Data(config_storage_t* data)
{
    if(data->magic != CONFIG_MAGIC_NUMBER)
    {
        return 0;
    }

    uint32_t calculated_checksum = Calculate_Config_Checksum(data);
    if(data->checksum != calculated_checksum)
    {
        return 0; 
    }

    if(data->ratio_value < 0.0f || data->ratio_value > 100.0f)
    {
        return 0;
    }

    if(data->limit_value < 0.0f || data->limit_value > 200.0f)
    {
        return 0; 
    }

    if(data->sample_interval < 1000 || data->sample_interval > 15000)
    {
        return 0;
    }
    return 1; // 数据有效其他其他情况数据无效
}

// 保存配置到Flash
uint8_t Save_Config_To_Flash(float ratio, float limit, uint32_t interval)
{
    config_storage_t config_data;

    // 检查变比值范围
    if(ratio < 0.0f || ratio > 100.0f)
    {
        return 0;
    }

    // 检查阈值范围
    if(limit < 0.0f || limit > 200.0f)
    {
        return 0;
    }

    // 检查采样间隔范围(1000-15000ms)
    if(interval < 1000 || interval > 15000)
    {
        return 0;
    }

    // 准备数据结构
    config_data.magic = CONFIG_MAGIC_NUMBER;
    config_data.ratio_value = ratio;
    config_data.limit_value = limit;
    config_data.sample_interval = interval; 
    config_data.checksum = Calculate_Config_Checksum(&config_data);

    // 擦除存储区域
    spi_flash_sector_erase(CONFIG_STORAGE_ADDR);
    spi_flash_wait_for_write_end();

    // 写入数据
    spi_flash_buffer_write((uint8_t*)&config_data, CONFIG_STORAGE_ADDR, sizeof(config_storage_t));
    spi_flash_wait_for_write_end();

    return 1; // 保存成功
}

// 从Flash读取配置
uint8_t Read_Config_From_Flash(float* ratio, float* limit, uint32_t* interval)
{
    config_storage_t config_data;

    if(ratio == NULL || limit == NULL || interval == NULL)
    {
        return 0; 
    }

    //从Flash读取数据
    spi_flash_buffer_read((uint8_t*)&config_data, CONFIG_STORAGE_ADDR, sizeof(config_storage_t));

    //校验数据
    if(!Verify_Config_Data(&config_data))
    {
        return 0;
    }

    *ratio = config_data.ratio_value;
    *limit = config_data.limit_value;
    *interval = config_data.sample_interval; // 返回采样间隔

    return 1; // 读取成功
}


// ==================== 三通道配置Flash存储函数 ====================

// 计算三通道配置校验和
uint32_t Calculate_Multi_Channel_Config_Checksum(multi_channel_config_t* data)
{
    uint32_t checksum = 0;
    uint8_t* ptr = (uint8_t*)data;

    // 计算除checksum字段外的所有字节的校验和
    for(int i = 0; i < sizeof(multi_channel_config_t) - sizeof(uint32_t); i++)
    {
        checksum += ptr[i];
    }

    return checksum;
}

// 校验三通道配置数据
uint8_t Verify_Multi_Channel_Config_Data(multi_channel_config_t* data)
{
    if(data->magic != MULTI_CONFIG_MAGIC_NUMBER)
    {
        return 0;
    }

    uint32_t calculated_checksum = Calculate_Multi_Channel_Config_Checksum(data);
    if(data->checksum != calculated_checksum)
    {
        return 0;
    }

    // 验证变比值范围 (0.1 ~ 100.0)
    if(data->ch0_ratio < 0.1f || data->ch0_ratio > 100.0f ||
       data->ch1_ratio < 0.1f || data->ch1_ratio > 100.0f ||
       data->ch2_ratio < 0.1f || data->ch2_ratio > 100.0f)
    {
        return 0;
    }

    // 验证阈值范围 (0.0 ~ 200.0)
    if(data->ch0_limit < 0.0f || data->ch0_limit > 200.0f ||
       data->ch1_limit < 0.0f || data->ch1_limit > 200.0f ||
       data->ch2_limit < 0.0f || data->ch2_limit > 200.0f)
    {
        return 0;
    }

    return 1; // 数据有效
}

// 保存三通道配置到Flash (采样间隔使用原有方式存储)
uint8_t Save_Multi_Channel_Config_To_Flash(float ch0_ratio, float ch1_ratio, float ch2_ratio,
                                          float ch0_limit, float ch1_limit, float ch2_limit)
{
    multi_channel_config_t config_data;

    // 检查变比值范围 (0.1 ~ 100.0)
    if(ch0_ratio < 0.1f || ch0_ratio > 100.0f ||
       ch1_ratio < 0.1f || ch1_ratio > 100.0f ||
       ch2_ratio < 0.1f || ch2_ratio > 100.0f)
    {
        return 0;
    }

    // 检查阈值范围 (0.0 ~ 200.0)
    if(ch0_limit < 0.0f || ch0_limit > 200.0f ||
       ch1_limit < 0.0f || ch1_limit > 200.0f ||
       ch2_limit < 0.0f || ch2_limit > 200.0f)
    {
        return 0;
    }

    // 准备数据结构 (不包含采样间隔)
    config_data.magic = MULTI_CONFIG_MAGIC_NUMBER;
    config_data.ch0_ratio = ch0_ratio;
    config_data.ch1_ratio = ch1_ratio;
    config_data.ch2_ratio = ch2_ratio;
    config_data.ch0_limit = ch0_limit;
    config_data.ch1_limit = ch1_limit;
    config_data.ch2_limit = ch2_limit;
    config_data.checksum = Calculate_Multi_Channel_Config_Checksum(&config_data);

    // 擦除存储区域
    spi_flash_sector_erase(MULTI_CONFIG_STORAGE_ADDR);
    spi_flash_wait_for_write_end();

    // 写入数据
    spi_flash_buffer_write((uint8_t*)&config_data, MULTI_CONFIG_STORAGE_ADDR, sizeof(multi_channel_config_t));
    spi_flash_wait_for_write_end();

    return 1; // 保存成功
}

// 从Flash读取三通道配置 (采样间隔使用原有方式读取)
uint8_t Read_Multi_Channel_Config_From_Flash(float* ch0_ratio, float* ch1_ratio, float* ch2_ratio,
                                            float* ch0_limit, float* ch1_limit, float* ch2_limit)
{
    multi_channel_config_t config_data;

    // 检查指针有效性
    if(ch0_ratio == NULL || ch1_ratio == NULL || ch2_ratio == NULL ||
       ch0_limit == NULL || ch1_limit == NULL || ch2_limit == NULL)
    {
        return 0;
    }

    // 从Flash读取数据
    spi_flash_buffer_read((uint8_t*)&config_data, MULTI_CONFIG_STORAGE_ADDR, sizeof(multi_channel_config_t));

    // 校验数据
    if(!Verify_Multi_Channel_Config_Data(&config_data))
    {
        return 0;
    }

    // 返回配置参数 (不包含采样间隔)
    *ch0_ratio = config_data.ch0_ratio;
    *ch1_ratio = config_data.ch1_ratio;
    *ch2_ratio = config_data.ch2_ratio;
    *ch0_limit = config_data.ch0_limit;
    *ch1_limit = config_data.ch1_limit;
    *ch2_limit = config_data.ch2_limit;

    return 1; // 读取成功
}

// ==================== 三通道配置Flash存储函数 ====================


//log系统初始化
uint8_t Log_Init(void)
{
    current_log_id = 0; // 初始值，实际值在创建文件时确定

    return 1; // 初始化成功
}

//累积日志缓存到Flash
uint8_t Cache_Log_To_Flash(const char* log_msg)
{
    uint8_t cache_buffer[LOG_CACHE_MAX_SIZE];
    uint32_t magic;
    uint16_t existing_len = 0;
    uint16_t new_msg_len = strlen(log_msg);

    if(new_msg_len > (LOG_CACHE_MAX_SIZE - 8))
    {
        return 0;//消息太长
    }
    if(strstr(log_msg, "system init") != NULL)
    {
        existing_len = 0;
        memset(cache_buffer, 0xFF, sizeof(cache_buffer));
    }
    else
    {
        spi_flash_buffer_read(cache_buffer, LOG_CACHE_ADDR, LOG_CACHE_MAX_SIZE);
        memcpy(&magic, cache_buffer, 4);
        if(magic == LOG_CACHE_MAGIC)
        {
            memcpy(&existing_len, cache_buffer + 4, 2);
            if(existing_len + new_msg_len > (LOG_CACHE_MAX_SIZE - 6))
            {
                return 0; // 空间不足
            }
            memcpy(cache_buffer + 6 + existing_len, log_msg, new_msg_len);
            existing_len += new_msg_len;
        }
        else
        {
            existing_len = 0;
            memset(cache_buffer, 0xFF, sizeof(cache_buffer));
        }
    }

    // 对于system init或新缓存，设置基本结构
    if(existing_len == 0)
    {
        magic = LOG_CACHE_MAGIC;
        memcpy(cache_buffer, &magic, 4);
        memcpy(cache_buffer + 6, log_msg, new_msg_len);
        existing_len = new_msg_len;
    }

    // 更新总长度
    memcpy(cache_buffer + 4, &existing_len, 2);

    // 擦除并写入Flash
    spi_flash_sector_erase(LOG_CACHE_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write(cache_buffer, LOG_CACHE_ADDR, existing_len + 6);
    spi_flash_wait_for_write_end();

    return 1; // 成功
}

// 检查Flash中是否有缓存的日志
uint8_t Check_Cached_Log_In_Flash(void)
{
    uint32_t magic;
    spi_flash_buffer_read((uint8_t*)&magic, LOG_CACHE_ADDR, 4);
    return (magic == LOG_CACHE_MAGIC) ? 1 : 0;
}

// 将Flash缓存的日志写入log0.txt
uint8_t Write_Cached_Log_To_Log0(void)
{
    uint8_t cache_buffer[LOG_CACHE_MAX_SIZE];
    uint32_t magic;
    uint16_t msg_len;
    FRESULT result;
    FIL log0_file;
    UINT bw;

    // 检查是否有缓存
    if(!Check_Cached_Log_In_Flash())
    {
        return 0; // 没有缓存
    }

    // 读取缓存数据
    spi_flash_buffer_read(cache_buffer, LOG_CACHE_ADDR, LOG_CACHE_MAX_SIZE);
    memcpy(&magic, cache_buffer, 4);
    memcpy(&msg_len, cache_buffer + 4, 2);

    // 验证数据
    if(magic != LOG_CACHE_MAGIC || msg_len > (LOG_CACHE_MAX_SIZE - 8))
    {
        return 0; // 数据无效
    }

    // 挂载文件系统
    if(f_mount(0, &fs) != FR_OK)
    {
        return 0; // 挂载失败
    }

    // 确保log文件夹存在
    f_mkdir("0:/log");

    // 打开log0.txt文件进行追加
    result = f_open(&log0_file, "0:/log/log0.txt", FA_OPEN_ALWAYS | FA_WRITE);
    if(result == FR_OK)
    {
        f_lseek(&log0_file, f_size(&log0_file));
        f_write(&log0_file, cache_buffer + 6, msg_len, &bw);
        f_sync(&log0_file);
        f_close(&log0_file);

        // 清除Flash缓存
        spi_flash_sector_erase(LOG_CACHE_ADDR);
        spi_flash_wait_for_write_end();

        f_mount(0, NULL);
        return 1; // 成功
    }

    f_mount(0, NULL);
    return 0; // 失败
}

// ==================== 【优化】最简洁的Flash存储函数实现 ====================

// 计算float存储校验和
uint32_t Calculate_Float_Checksum(float_storage_t* data)
{
    if(data == NULL) return 0;

    uint32_t checksum = data->magic;
    for(int i = 0; i < 10; i++)
    {
        uint32_t* float_as_uint = (uint32_t*)&(data->values[i]);
        checksum ^= *float_as_uint;
    }
    return checksum;
}

/**
 * @brief 保存float数组到Flash - 完全模仿您的Save_Config_To_Flash
 * @param values: 10个float值的数组
 * @return 1=成功, 0=失败
 */
uint8_t Save_Float_Array_To_Flash(float values[10])
{
    float_storage_t float_data;

    if(values == NULL)
    {
        return 0;
    }

    // 填充数据结构 - 完全模仿您的代码
    float_data.magic = FLOAT_ARRAY_MAGIC_NUMBER;
    for(int i = 0; i < 10; i++)
    {
        float_data.values[i] = values[i];
    }
    float_data.checksum = Calculate_Float_Checksum(&float_data);

    // 擦除存储区域 - 完全按照您的代码
    spi_flash_sector_erase(FLOAT_ARRAY_STORAGE_ADDR);
    spi_flash_wait_for_write_end();

    // 写入数据 - 完全按照您的代码
    spi_flash_buffer_write((uint8_t*)&float_data, FLOAT_ARRAY_STORAGE_ADDR, sizeof(float_storage_t));
    spi_flash_wait_for_write_end();

    return 1; // 保存成功
}

/**
 * @brief 从Flash读取float数组 - 完全模仿您的Read_Config_From_Flash
 * @param values: 输出的10个float值数组
 * @return 1=成功, 0=失败
 */
uint8_t Read_Float_Array_From_Flash(float values[10])
{
    float_storage_t float_data;

    if(values == NULL)
    {
        return 0;
    }

    // 从Flash读取数据 - 完全按照您的代码
    spi_flash_buffer_read((uint8_t*)&float_data, FLOAT_ARRAY_STORAGE_ADDR, sizeof(float_storage_t));

    // 校验魔数 - 完全模仿您的代码
    if(float_data.magic != FLOAT_ARRAY_MAGIC_NUMBER)
    {
        return 0; // 魔数不匹配
    }

    // 校验checksum - 完全模仿您的代码
    uint32_t calculated_checksum = Calculate_Float_Checksum(&float_data);
    if(calculated_checksum != float_data.checksum)
    {
        return 0; // 校验失败
    }

    // 复制数据 - 完全模仿您的代码
    for(int i = 0; i < 10; i++)
    {
        values[i] = float_data.values[i];
    }

    return 1; // 读取成功
}

/******************** 简化版本 - 单个float变量存储 *******************/

/**
 * @brief 保存单个float变量到指定位置
 * @param value: 要保存的float值
 * @param position: 存储位置 (0-9)
 * @return 1=成功, 0=失败
 */
uint8_t Save_Single_Float(float value, uint8_t position)
{
    if(position >= 10)
    {
        return 0; // 位置超出范围
    }

    // 先读取现有数据
    float current_values[10] = {0};
    Read_Float_Array_From_Flash(current_values); // 不检查返回值，如果失败就用全0数组

    // 更新指定位置的值
    current_values[position] = value;

    // 保存回Flash
    return Save_Float_Array_To_Flash(current_values);
}

/**
 * @brief 从指定位置读取单个float变量
 * @param value: 输出的float值指针
 * @param position: 读取位置 (0-9)
 * @return 1=成功, 0=失败
 */
uint8_t Load_Single_Float(float* value, uint8_t position)
{
    if(value == NULL || position >= 10)
    {
        return 0; // 参数无效
    }

    // 读取整个数组
    float current_values[10] = {0};
    uint8_t result = Read_Float_Array_From_Flash(current_values);

    if(result == 1)
    {
        *value = current_values[position];
        return 1; // 成功
    }
    else
    {
        *value = 0.0f; // 失败时返回0
        return 0; // 失败
    }
}

// ==================== 【优化结束】最简洁的Flash存储函数实现 ====================
